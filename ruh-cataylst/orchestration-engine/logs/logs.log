2025-06-28 00:44:59 - Main - INFO - Starting Server
2025-06-28 00:44:59 - Main - INFO - Connection at: **************:9092
2025-06-28 00:44:59 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-28 00:44:59 - NodeExecutor - INFO - NodeExecutor initialized.
2025-06-28 00:44:59 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-28 00:44:59 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-28 00:44:59 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 00:45:01 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 00:45:01 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-28 00:45:03 - Red<PERSON><PERSON>anager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-28 00:45:05 - PostgresManager - INFO - PostgreSQL connection pool created
2025-06-28 00:45:05 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-06-28 00:45:07 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 00:45:08 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-06-28 00:45:08 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 00:45:10 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 00:45:10 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-28 00:45:11 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-28 00:45:11 - RedisEventListener - INFO - Starting Redis event listener thread
2025-06-28 00:45:11 - RedisEventListener - INFO - Redis event listener started
2025-06-28 00:45:11 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-06-28 00:45:11 - StateManager - DEBUG - Using provided database connections
2025-06-28 00:45:11 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 00:45:11 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 00:45:11 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 00:45:12 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-06-28 00:45:12 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 00:45:12 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 00:45:12 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-06-28 00:45:12 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-06-28 00:45:13 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-06-28 00:45:13 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-06-28 00:45:15 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-06-28 00:45:15 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-06-28 00:45:15 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-06-28 00:45:20 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-06-28 00:45:26 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-06-28 00:45:26 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-06-28 00:45:26 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-06-28 00:45:32 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-06-28 00:45:32 - NodeExecutor - INFO - Background result consumer loop started.
2025-06-28 00:45:32 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-06-28 00:45:39 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-06-28 00:45:39 - AgentExecutor - INFO - Background result consumer loop started.
2025-06-28 00:46:07 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=991
2025-06-28 00:46:07 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1751051767, 'task_type': 'workflow', 'data': {'workflow_id': 'feda07bf-a91e-4004-80cb-72416cdb5a43', 'payload': {'user_dependent_fields': ['start'], 'user_payload_template': {'start': {'value': '1', 'transition_id': 'LoopNode-*************'}}}, 'approval': True, 'user_id': 'c1454e90-09ac-40f2-bde2-833387d7b645'}, 'approval': True}
2025-06-28 00:46:07 - KafkaWorkflowConsumer - INFO - Extracted user_id: c1454e90-09ac-40f2-bde2-833387d7b645 for workflow: feda07bf-a91e-4004-80cb-72416cdb5a43
2025-06-28 00:46:07 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/feda07bf-a91e-4004-80cb-72416cdb5a43
2025-06-28 00:46:08 - WorkflowService - DEBUG - Received response with status code: 200
2025-06-28 00:46:08 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow each loop testing retrieved successfully",
  "workflow": {
    "id": "feda07bf-a91e-4004-80cb-72416cdb5a43",
    "name": "each loop testing",
    "description": "each_loop_testing",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/0ae8b7a0-c493-40d6-b818-8752abb00acc.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/614a1d03-7cc9-4fc0-aec1-80cbe383f8b6.json",
    "start_nodes": [
      {
        "field": "start",
        "type": "string",
        "transition_id": "transition-LoopNode-*************"
      }
    ],
    "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645",
    "user_ids": [
      "c1454e90-09ac-40f2-bde2-833387d7b645"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-24T11:13:46.129953",
    "updated_at": "2025-06-27T16:42:33.943009",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      },
      {
        "name": "MessageToDataComponent",
        "display_name": "Message To Data",
        "type": "component",
        "transition_id": "transition-MessageToDataComponent-1750769557490"
      },
      {
        "name": "LoopNode",
        "display_name": "For Each Loop",
        "type": "component",
        "transition_id": "transition-LoopNode-*************"
      }
    ],
    "is_updated": true
  }
}
2025-06-28 00:46:08 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for feda07bf-a91e-4004-80cb-72416cdb5a43 - server_script_path is optional
2025-06-28 00:46:08 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-06-28 00:46:08 - StateManager - DEBUG - Using global database connections from initializer
2025-06-28 00:46:08 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 00:46:08 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 00:46:08 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 00:46:08 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 00:46:08 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 00:46:08 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 00:46:08 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 00:46:09 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 00:46:09 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 00:46:09 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-06-28 00:46:09 - StateManager - DEBUG - Using provided database connections
2025-06-28 00:46:09 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 00:46:09 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 00:46:09 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 00:46:09 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 00:46:09 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 00:46:09 - StateManager - DEBUG - Extracted dependencies for transition transition-MessageToDataComponent-1750769557490: ['transition-LoopNode-*************']
2025-06-28 00:46:09 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-LoopNode-*************']
2025-06-28 00:46:09 - StateManager - INFO - Built dependency map for 3 transitions
2025-06-28 00:46:09 - StateManager - DEBUG - Transition transition-MessageToDataComponent-1750769557490 depends on: ['transition-LoopNode-*************']
2025-06-28 00:46:09 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* depends on: ['transition-LoopNode-*************']
2025-06-28 00:46:09 - MCPToolExecutor - DEBUG - Set correlation ID to: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad
2025-06-28 00:46:09 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 5af9a7b9-62bf-48c0-9b3e-02299ff277ad in tool_executor
2025-06-28 00:46:09 - MCPToolExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-06-28 00:46:09 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in tool_executor
2025-06-28 00:46:09 - NodeExecutor - DEBUG - Set correlation ID to: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad
2025-06-28 00:46:09 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 5af9a7b9-62bf-48c0-9b3e-02299ff277ad in node_executor
2025-06-28 00:46:09 - AgentExecutor - DEBUG - Set correlation ID to: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad
2025-06-28 00:46:09 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 5af9a7b9-62bf-48c0-9b3e-02299ff277ad in agent_executor
2025-06-28 00:46:09 - AgentExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-06-28 00:46:09 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in agent_executor
2025-06-28 00:46:09 - TransitionHandler - INFO - TransitionHandler initialized
2025-06-28 00:46:09 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad
2025-06-28 00:46:09 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad
2025-06-28 00:46:09 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-06-28 00:46:09 - StateManager - INFO - Workflow initialized with initial transition: transition-LoopNode-*************
2025-06-28 00:46:09 - StateManager - DEBUG - State: pending={'transition-LoopNode-*************'}, waiting=set(), completed=set()
2025-06-28 00:46:09 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-LoopNode-*************
2025-06-28 00:46:09 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-28 00:46:10 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:5af9a7b9-62bf-48c0-9b3e-02299ff277ad'
2025-06-28 00:46:10 - RedisManager - DEBUG - Set key 'workflow_state:5af9a7b9-62bf-48c0-9b3e-02299ff277ad' with TTL of 600 seconds
2025-06-28 00:46:10 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 00:46:10 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 00:46:10 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 00:46:10 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-28 00:46:10 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 00:46:10 - StateManager - INFO - Terminated: False
2025-06-28 00:46:10 - StateManager - INFO - Pending transitions (0): []
2025-06-28 00:46:10 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 00:46:10 - StateManager - INFO - Completed transitions (0): []
2025-06-28 00:46:10 - StateManager - INFO - Results stored for 0 transitions
2025-06-28 00:46:10 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 00:46:10 - StateManager - INFO - Workflow status: inactive
2025-06-28 00:46:10 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 00:46:10 - StateManager - INFO - Workflow status: inactive
2025-06-28 00:46:10 - StateManager - INFO - Workflow paused: False
2025-06-28 00:46:10 - StateManager - INFO - ==============================
2025-06-28 00:46:10 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-28 00:46:10 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id 5af9a7b9-62bf-48c0-9b3e-02299ff277ad):
2025-06-28 00:46:10 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-06-28 00:46:10 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=initial, execution_type=loop)
2025-06-28 00:46:10 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-28 00:46:10 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-28 00:46:10 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-28 00:46:10 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-28 00:46:10 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-28 00:46:10 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-28 00:46:10 - WorkflowUtils - DEBUG - Filtering out field 'iteration_list' with empty collection: []
2025-06-28 00:46:10 - WorkflowUtils - INFO - 🧹 Parameter filtering: 14 → 13 fields (1 null/empty fields removed)
2025-06-28 00:46:10 - TransitionHandler - DEBUG - tool Parameters: {'source_type': 'number_range', 'batch_size': '1', 'start': '1', 'end': '3', 'step': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 00:46:10 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'source_type': 'number_range', 'batch_size': '1', 'start': '1', 'end': '3', 'step': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 00:46:10 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id 5af9a7b9-62bf-48c0-9b3e-02299ff277ad):
2025-06-28 00:46:10 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-28 00:46:10 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-28 00:46:10 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-28 00:46:10 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-28 00:46:10 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MessageToDataComponent-1750769557490 (has final/aggregated indicators)
2025-06-28 00:46:10 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-28 00:46:10 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-28 00:46:10 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: 1
2025-06-28 00:46:11 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-28 00:46:12 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-28 00:46:12 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 00:46:12 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-28 00:46:12 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0'}
2025-06-28 00:46:12 - StateManager - DEBUG - Stored result for transition current_iteration in memory: 1
2025-06-28 00:46:12 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-28 00:46:12 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-28 00:46:12 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 00:46:12 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-28 00:46:12 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'current_iteration'}
2025-06-28 00:46:12 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 240214.2514485, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 2}}
2025-06-28 00:46:13 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 00:46:13 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 00:46:13 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 00:46:13 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 00:46:13 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'transition-LoopNode-*************', 'current_iteration'}
2025-06-28 00:46:13 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-28 00:46:13 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id 5af9a7b9-62bf-48c0-9b3e-02299ff277ad):
2025-06-28 00:46:13 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 2, 'workflow_status': 'running'}
2025-06-28 00:46:13 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-28 00:46:13 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 00:46:13 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-28 00:46:13 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 00:46:13 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 00:46:14 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 00:46:14 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-28 00:46:14 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 00:46:14 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 00:46:14 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 240214.2514485, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 2}}
2025-06-28 00:46:14 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 00:46:14 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 00:46:14 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 00:46:14 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 00:46:14 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 240214.2514485, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 2}}
2025-06-28 00:46:14 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 00:46:14 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 00:46:14 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': 1
2025-06-28 00:46:14 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 00:46:14 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 00:46:14 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-28 00:46:14 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-28 00:46:14 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-28 00:46:14 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-28 00:46:14 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-28 00:46:14 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-28 00:46:14 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-28 00:46:14 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-28 00:46:14 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-28 00:46:14 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-28 00:46:14 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 3 fields (10 null/empty fields removed)
2025-06-28 00:46:14 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 00:46:14 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-06-28 00:46:14 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = coming
2025-06-28 00:46:14 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 1, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 00:46:14 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 1, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 00:46:14 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 1, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 00:46:14 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id 5af9a7b9-62bf-48c0-9b3e-02299ff277ad):
2025-06-28 00:46:14 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 3, 'workflow_status': 'running'}
2025-06-28 00:46:14 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 64a54821-8252-4225-a0c6-4f33172c9384) using provided producer.
2025-06-28 00:46:14 - NodeExecutor - DEBUG - Added correlation_id 5af9a7b9-62bf-48c0-9b3e-02299ff277ad to payload
2025-06-28 00:46:14 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-28 00:46:14 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 1, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}, 'request_id': '64a54821-8252-4225-a0c6-4f33172c9384', 'correlation_id': '5af9a7b9-62bf-48c0-9b3e-02299ff277ad', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-28 00:46:14 - NodeExecutor - DEBUG - Request 64a54821-8252-4225-a0c6-4f33172c9384 sent successfully using provided producer.
2025-06-28 00:46:14 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 64a54821-8252-4225-a0c6-4f33172c9384...
2025-06-28 00:46:14 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 991, corr_id: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad
2025-06-28 00:46:15 - NodeExecutor - DEBUG - Result consumer received message: Offset=802
2025-06-28 00:46:15 - NodeExecutor - DEBUG - Received valid result for request_id 64a54821-8252-4225-a0c6-4f33172c9384
2025-06-28 00:46:15 - NodeExecutor - INFO - Result received for request 64a54821-8252-4225-a0c6-4f33172c9384.
2025-06-28 00:46:15 - TransitionHandler - INFO - Execution result from Components executor: "1\ncoming"
2025-06-28 00:46:15 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id 5af9a7b9-62bf-48c0-9b3e-02299ff277ad):
2025-06-28 00:46:15 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': '1\ncoming', 'status': 'completed', 'sequence': 4, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 00:46:15 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': '1\ncoming'}, 'status': 'completed', 'timestamp': 1751051775.7979991}}
2025-06-28 00:46:16 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-28 00:46:16 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-28 00:46:16 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 00:46:16 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 00:46:16 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'loop_iteration_0', 'transition-LoopNode-*************', 'current_iteration'}
2025-06-28 00:46:16 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-28 00:46:16 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-28 00:46:16 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-28 00:46:16 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 00:46:16 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-28 00:46:16 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-28 00:46:16 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 00:46:16 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 00:46:16 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-28 00:46:16 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.98 seconds
2025-06-28 00:46:16 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id 5af9a7b9-62bf-48c0-9b3e-02299ff277ad):
2025-06-28 00:46:16 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad, response: {'result': 'Completed transition in 2.98 seconds', 'message': 'Transition completed in 2.98 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 5, 'workflow_status': 'running'}
2025-06-28 00:46:16 - StateManager - DEBUG - Stored result for transition loop_iteration_1 in memory: 2
2025-06-28 00:46:16 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_1
2025-06-28 00:46:17 - RedisManager - DEBUG - Set key 'result:loop_iteration_1' with TTL of 900 seconds
2025-06-28 00:46:17 - StateManager - DEBUG - Stored result for transition loop_iteration_1 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 00:46:17 - StateManager - INFO - Marked transition loop_iteration_1 as completed (was_pending=False, was_waiting=False)
2025-06-28 00:46:17 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0', 'loop_iteration_1', 'transition-CombineTextComponent-*************', 'transition-LoopNode-*************'}
2025-06-28 00:46:17 - StateManager - DEBUG - Stored result for transition current_iteration in memory: 2
2025-06-28 00:46:17 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-28 00:46:18 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-28 00:46:18 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 00:46:18 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-28 00:46:18 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0', 'loop_iteration_1', 'transition-CombineTextComponent-*************', 'transition-LoopNode-*************'}
2025-06-28 00:46:18 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 2, 'iteration_index': 1, 'iteration_metadata': {'timestamp': 240219.935636666, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 2}}
2025-06-28 00:46:19 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 00:46:19 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 00:46:19 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 00:46:19 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 00:46:19 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0', 'loop_iteration_1', 'transition-CombineTextComponent-*************', 'transition-LoopNode-*************'}
2025-06-28 00:46:19 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-28 00:46:19 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 6, corr_id 5af9a7b9-62bf-48c0-9b3e-02299ff277ad):
2025-06-28 00:46:19 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 6, 'workflow_status': 'running'}
2025-06-28 00:46:19 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-28 00:46:19 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 00:46:19 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-28 00:46:19 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 00:46:19 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 00:46:20 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 00:46:20 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-28 00:46:20 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 00:46:20 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 00:46:20 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 2, 'iteration_index': 1, 'iteration_metadata': {'timestamp': 240219.935636666, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 2}}
2025-06-28 00:46:20 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 00:46:20 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 00:46:20 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 00:46:20 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 00:46:20 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 2, 'iteration_index': 1, 'iteration_metadata': {'timestamp': 240219.935636666, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 2}}
2025-06-28 00:46:20 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 00:46:20 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 00:46:20 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': 2
2025-06-28 00:46:20 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 00:46:20 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 00:46:20 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-28 00:46:20 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-28 00:46:20 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-28 00:46:20 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-28 00:46:20 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-28 00:46:20 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-28 00:46:20 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-28 00:46:20 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-28 00:46:20 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-28 00:46:20 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-28 00:46:20 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 3 fields (10 null/empty fields removed)
2025-06-28 00:46:20 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 00:46:20 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-06-28 00:46:20 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = coming
2025-06-28 00:46:20 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 2, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 00:46:20 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 2, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 00:46:20 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 2, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 00:46:20 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 7, corr_id 5af9a7b9-62bf-48c0-9b3e-02299ff277ad):
2025-06-28 00:46:20 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 7, 'workflow_status': 'running'}
2025-06-28 00:46:20 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 4951a6b8-9eb5-4e45-9470-49a6eb34d9c1) using provided producer.
2025-06-28 00:46:20 - NodeExecutor - DEBUG - Added correlation_id 5af9a7b9-62bf-48c0-9b3e-02299ff277ad to payload
2025-06-28 00:46:20 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-28 00:46:20 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 2, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}, 'request_id': '4951a6b8-9eb5-4e45-9470-49a6eb34d9c1', 'correlation_id': '5af9a7b9-62bf-48c0-9b3e-02299ff277ad', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-28 00:46:20 - NodeExecutor - DEBUG - Request 4951a6b8-9eb5-4e45-9470-49a6eb34d9c1 sent successfully using provided producer.
2025-06-28 00:46:20 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 4951a6b8-9eb5-4e45-9470-49a6eb34d9c1...
2025-06-28 00:46:20 - NodeExecutor - DEBUG - Result consumer received message: Offset=803
2025-06-28 00:46:20 - NodeExecutor - DEBUG - Received valid result for request_id 4951a6b8-9eb5-4e45-9470-49a6eb34d9c1
2025-06-28 00:46:20 - NodeExecutor - INFO - Result received for request 4951a6b8-9eb5-4e45-9470-49a6eb34d9c1.
2025-06-28 00:46:20 - TransitionHandler - INFO - Execution result from Components executor: "2\ncoming"
2025-06-28 00:46:20 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 8, corr_id 5af9a7b9-62bf-48c0-9b3e-02299ff277ad):
2025-06-28 00:46:20 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': '2\ncoming', 'status': 'completed', 'sequence': 8, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 00:46:20 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': '2\ncoming'}, 'status': 'completed', 'timestamp': 1751051780.8192708}}
2025-06-28 00:46:21 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-28 00:46:21 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-28 00:46:21 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 00:46:21 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 00:46:21 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0', 'loop_iteration_1', 'transition-CombineTextComponent-*************', 'transition-LoopNode-*************'}
2025-06-28 00:46:21 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-28 00:46:21 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-28 00:46:21 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-28 00:46:21 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 00:46:21 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-28 00:46:21 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-28 00:46:21 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 00:46:21 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 00:46:21 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-28 00:46:21 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.25 seconds
2025-06-28 00:46:21 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 9, corr_id 5af9a7b9-62bf-48c0-9b3e-02299ff277ad):
2025-06-28 00:46:21 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad, response: {'result': 'Completed transition in 2.25 seconds', 'message': 'Transition completed in 2.25 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 9, 'workflow_status': 'running'}
2025-06-28 00:46:21 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 10, corr_id 5af9a7b9-62bf-48c0-9b3e-02299ff277ad):
2025-06-28 00:46:21 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad, response: {'status': 'loop_completed', 'metadata': {'success': True, 'final_results': ['1\ncoming', '2\ncoming'], 'iteration_count': 2, 'total_iterations': 2}, 'transition_id': 'transition-LoopNode-*************', 'sequence': 10, 'workflow_status': 'running'}
2025-06-28 00:46:21 - TransitionHandler - DEBUG - 🗑️ Unregistered loop executor for transition: transition-LoopNode-*************
2025-06-28 00:46:21 - TransitionHandler - INFO - Execution result from loop executor: {
  "final_results": [
    "1\ncoming",
    "2\ncoming"
  ]
}
2025-06-28 00:46:21 - TransitionHandler - INFO - Checking execution result for errors: {
  "final_results": [
    "1\ncoming",
    "2\ncoming"
  ]
}
2025-06-28 00:46:21 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 11, corr_id 5af9a7b9-62bf-48c0-9b3e-02299ff277ad):
2025-06-28 00:46:21 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Transition Result received.', 'result': {'final_results': ['1\ncoming', '2\ncoming']}, 'status': 'completed', 'sequence': 11, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 00:46:21 - TransitionHandler - INFO - 🔄 Handling loop completion for transition: transition-LoopNode-*************
2025-06-28 00:46:21 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'final_results': ['1\ncoming', '2\ncoming']}
2025-06-28 00:46:22 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 00:46:22 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 00:46:22 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 00:46:22 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 00:46:22 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0', 'loop_iteration_1', 'transition-CombineTextComponent-*************', 'transition-LoopNode-*************'}
2025-06-28 00:46:22 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-28 00:46:22 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MessageToDataComponent-1750769557490 (has final/aggregated indicators)
2025-06-28 00:46:22 - TransitionHandler - INFO - 🔍 Auto-detected loop body transitions: ['transition-CombineTextComponent-*************']
2025-06-28 00:46:22 - TransitionHandler - DEBUG - 🔍 Loop completion filtering - Loop config: {'iteration_behavior': 'independent', 'iteration_source': {'number_range': {'start': 1, 'end': 3}, 'step': 1}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}}
2025-06-28 00:46:22 - TransitionHandler - DEBUG - 🔍 Loop body transitions to filter: ['transition-CombineTextComponent-*************']
2025-06-28 00:46:22 - TransitionHandler - DEBUG - 🔍 Output data configs: [{'to_transition_id': 'transition-CombineTextComponent-*************', 'target_node_id': 'Combine Text', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'current_item', 'result_path': 'current_item', 'edge_id': 'reactflow__edge-LoopNode-*************current_item-CombineTextComponent-*************main_input'}]}}, {'to_transition_id': 'transition-MessageToDataComponent-1750769557490', 'target_node_id': 'Message To Data', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'final_results', 'result_path': 'final_results', 'edge_id': 'reactflow__edge-LoopNode-*************final_results-MessageToDataComponent-1750769557490input_message'}]}}]
2025-06-28 00:46:22 - TransitionHandler - DEBUG - 🚫 Skipping loop body transition 'transition-CombineTextComponent-*************' - should have been executed internally by loop executor
2025-06-28 00:46:22 - TransitionHandler - DEBUG - ✅ Adding exit transition 'transition-MessageToDataComponent-1750769557490' to next transitions
2025-06-28 00:46:22 - TransitionHandler - INFO - ✅ Loop completion handled for transition-LoopNode-*************. Next transitions: ['transition-MessageToDataComponent-1750769557490']
2025-06-28 00:46:22 - TransitionHandler - INFO - Completed transition transition-LoopNode-************* in 11.49 seconds
2025-06-28 00:46:22 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 12, corr_id 5af9a7b9-62bf-48c0-9b3e-02299ff277ad):
2025-06-28 00:46:22 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad, response: {'result': 'Completed transition in 11.49 seconds', 'message': 'Transition completed in 11.49 seconds', 'transition_id': 'transition-LoopNode-*************', 'status': 'time_logged', 'sequence': 12, 'workflow_status': 'running'}
2025-06-28 00:46:22 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-MessageToDataComponent-1750769557490']]
2025-06-28 00:46:22 - EnhancedWorkflowEngine - INFO - Transition transition-LoopNode-************* completed successfully: 1 next transitions
2025-06-28 00:46:22 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-MessageToDataComponent-1750769557490']
2025-06-28 00:46:22 - EnhancedWorkflowEngine - INFO - Adding transition transition-MessageToDataComponent-1750769557490 to pending (all dependencies met)
2025-06-28 00:46:22 - StateManager - DEBUG - Workflow active: {'transition-MessageToDataComponent-1750769557490'}
2025-06-28 00:46:23 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:5af9a7b9-62bf-48c0-9b3e-02299ff277ad'
2025-06-28 00:46:23 - RedisManager - DEBUG - Set key 'workflow_state:5af9a7b9-62bf-48c0-9b3e-02299ff277ad' with TTL of 600 seconds
2025-06-28 00:46:23 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 00:46:23 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 00:46:23 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 00:46:23 - StateManager - INFO - Cleared 1 pending transitions: {'transition-MessageToDataComponent-1750769557490'}
2025-06-28 00:46:23 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 00:46:23 - StateManager - INFO - Terminated: False
2025-06-28 00:46:23 - StateManager - INFO - Pending transitions (0): []
2025-06-28 00:46:23 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 00:46:23 - StateManager - INFO - Completed transitions (5): ['current_iteration', 'loop_iteration_0', 'loop_iteration_1', 'transition-CombineTextComponent-*************', 'transition-LoopNode-*************']
2025-06-28 00:46:23 - StateManager - INFO - Results stored for 5 transitions
2025-06-28 00:46:23 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 00:46:23 - StateManager - INFO - Workflow status: inactive
2025-06-28 00:46:23 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 00:46:23 - StateManager - INFO - Workflow status: inactive
2025-06-28 00:46:23 - StateManager - INFO - Workflow paused: False
2025-06-28 00:46:23 - StateManager - INFO - ==============================
2025-06-28 00:46:23 - TransitionHandler - INFO - Starting parallel execution of transition: transition-MessageToDataComponent-1750769557490
2025-06-28 00:46:23 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 13, corr_id 5af9a7b9-62bf-48c0-9b3e-02299ff277ad):
2025-06-28 00:46:23 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad, response: {'result': 'Starting execution of transition: transition-MessageToDataComponent-1750769557490', 'message': 'Starting execution...', 'transition_id': 'transition-MessageToDataComponent-1750769557490', 'status': 'started', 'sequence': 13, 'workflow_status': 'running'}
2025-06-28 00:46:23 - TransitionHandler - EXECUTE - Transition 'transition-MessageToDataComponent-1750769557490' (type=standard, execution_type=Components)
2025-06-28 00:46:23 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 00:46:23 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-MessageToDataComponent-1750769557490
2025-06-28 00:46:23 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 00:46:23 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 00:46:24 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 00:46:24 - StateManager - DEBUG - Extracted results for 1 tools in transition transition-LoopNode-*************
2025-06-28 00:46:24 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 00:46:24 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 00:46:24 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_results': {'final_results': ['1\ncoming', '2\ncoming']}
2025-06-28 00:46:24 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-28 00:46:24 - WorkflowUtils - DEBUG - Path tracking - Found handle 'final_results' directly in source_results
2025-06-28 00:46:24 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 00:46:24 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 00:46:24 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_results': {'final_results': ['1\ncoming', '2\ncoming']}
2025-06-28 00:46:24 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-28 00:46:24 - WorkflowUtils - DEBUG - Path tracking - Found handle 'final_results' directly in source_results
2025-06-28 00:46:24 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_results → input_message via path 'final_results': ['1\ncoming', '2\ncoming']
2025-06-28 00:46:24 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 00:46:24 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 00:46:24 - WorkflowUtils - DEBUG - Filtering out field 'input_message' with null/empty value: None
2025-06-28 00:46:24 - WorkflowUtils - DEBUG - Filtering out field 'fields_to_extract' with null/empty value: None
2025-06-28 00:46:24 - WorkflowUtils - INFO - 🧹 Parameter filtering: 2 → 0 fields (2 null/empty fields removed)
2025-06-28 00:46:24 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_message': ['1\ncoming', '2\ncoming']}
2025-06-28 00:46:24 - TransitionHandler - DEBUG - tool Parameters: {'input_message': ['1\ncoming', '2\ncoming']}
2025-06-28 00:46:24 - TransitionHandler - INFO - Invoking tool 'MessageToDataComponent' (tool_id: 1) for node 'MessageToDataComponent' in transition 'transition-MessageToDataComponent-1750769557490' with parameters: {'input_message': ['1\ncoming', '2\ncoming']}
2025-06-28 00:46:24 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 14, corr_id 5af9a7b9-62bf-48c0-9b3e-02299ff277ad):
2025-06-28 00:46:24 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad, response: {'transition_id': 'transition-MessageToDataComponent-1750769557490', 'node_id': 'MessageToDataComponent', 'tool_name': 'MessageToDataComponent', 'message': 'Connecting to server', 'result': 'Connecting to server MessageToDataComponent', 'status': 'connecting', 'sequence': 14, 'workflow_status': 'running'}
2025-06-28 00:46:24 - NodeExecutor - INFO - Executing tool 'MessageToDataComponent' via Kafka (request_id: dca55cc7-d0a5-47a8-8d4f-64ff25d51f65) using provided producer.
2025-06-28 00:46:24 - NodeExecutor - DEBUG - Added correlation_id 5af9a7b9-62bf-48c0-9b3e-02299ff277ad to payload
2025-06-28 00:46:24 - NodeExecutor - DEBUG - Added transition_id transition-MessageToDataComponent-1750769557490 to payload
2025-06-28 00:46:24 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'MessageToDataComponent', 'tool_parameters': {'input_message': ['1\ncoming', '2\ncoming']}, 'request_id': 'dca55cc7-d0a5-47a8-8d4f-64ff25d51f65', 'correlation_id': '5af9a7b9-62bf-48c0-9b3e-02299ff277ad', 'transition_id': 'transition-MessageToDataComponent-1750769557490'}
2025-06-28 00:46:24 - NodeExecutor - DEBUG - Request dca55cc7-d0a5-47a8-8d4f-64ff25d51f65 sent successfully using provided producer.
2025-06-28 00:46:24 - NodeExecutor - DEBUG - Waiting indefinitely for result for request dca55cc7-d0a5-47a8-8d4f-64ff25d51f65...
2025-06-28 00:46:24 - NodeExecutor - DEBUG - Result consumer received message: Offset=804
2025-06-28 00:46:24 - NodeExecutor - WARNING - Received error response for request_id dca55cc7-d0a5-47a8-8d4f-64ff25d51f65: Cannot extract all fields from the input message. Please specify fields to extract.
2025-06-28 00:46:24 - NodeExecutor - ERROR - Error during node execution dca55cc7-d0a5-47a8-8d4f-64ff25d51f65: Node execution failed: Cannot extract all fields from the input message. Please specify fields to extract.
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Cannot extract all fields from the input message. Please specify fields to extract.
2025-06-28 00:46:24 - TransitionHandler - ERROR - Tool execution failed for tool 'MessageToDataComponent' (tool_id: 1) in node 'MessageToDataComponent' of transition 'transition-MessageToDataComponent-1750769557490': Node execution failed: Cannot extract all fields from the input message. Please specify fields to extract.Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 463, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Cannot extract all fields from the input message. Please specify fields to extract.

2025-06-28 00:46:24 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 15, corr_id 5af9a7b9-62bf-48c0-9b3e-02299ff277ad):
2025-06-28 00:46:24 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad, response: {'transition_id': 'transition-MessageToDataComponent-1750769557490', 'node_id': 'MessageToDataComponent', 'tool_name': 'MessageToDataComponent', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Node execution failed: Cannot extract all fields from the input message. Please specify fields to extract.', 'status': 'failed', 'sequence': 15, 'workflow_status': 'running'}
2025-06-28 00:46:24 - TransitionHandler - ERROR - Exception in transition transition-MessageToDataComponent-1750769557490: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Cannot extract all fields from the input message. Please specify fields to extract.
2025-06-28 00:46:24 - EnhancedWorkflowEngine - DEBUG - Results: [Exception('Exception in transition transition-MessageToDataComponent-1750769557490: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Cannot extract all fields from the input message. Please specify fields to extract.')]
2025-06-28 00:46:24 - EnhancedWorkflowEngine - ERROR - Error in execution of transition transition-MessageToDataComponent-1750769557490: Exception in transition transition-MessageToDataComponent-1750769557490: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Cannot extract all fields from the input message. Please specify fields to extract.
2025-06-28 00:46:24 - EnhancedWorkflowEngine - ERROR - Traceback for transition transition-MessageToDataComponent-1750769557490: NoneType: None

2025-06-28 00:46:24 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during parallel execution of standard transitions: Exception in transition transition-MessageToDataComponent-1750769557490: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Cannot extract all fields from the input message. Please specify fields to extract.
2025-06-28 00:46:24 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 463, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Cannot extract all fields from the input message. Please specify fields to extract.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 674, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Cannot extract all fields from the input message. Please specify fields to extract.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 263, in execute
    raise result
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 227, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-MessageToDataComponent-1750769557490: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Cannot extract all fields from the input message. Please specify fields to extract.

2025-06-28 00:46:24 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during workflow execution: Exception in transition transition-MessageToDataComponent-1750769557490: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Cannot extract all fields from the input message. Please specify fields to extract.
2025-06-28 00:46:24 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 463, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Cannot extract all fields from the input message. Please specify fields to extract.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 674, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Cannot extract all fields from the input message. Please specify fields to extract.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 280, in execute
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 263, in execute
    raise result
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 227, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-MessageToDataComponent-1750769557490: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Cannot extract all fields from the input message. Please specify fields to extract.

2025-06-28 00:46:24 - KafkaWorkflowConsumer - ERROR - Exception in workflow execution: Exception in transition transition-MessageToDataComponent-1750769557490: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Cannot extract all fields from the input message. Please specify fields to extract.
2025-06-28 00:46:24 - KafkaWorkflowConsumer - INFO - Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' final status: failed, result: Exception in workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43': Exception in transition transition-MessageToDataComponent-1750769557490: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Cannot extract all fields from the input message. Please specify fields to extract.
2025-06-28 00:46:24 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad, response: {'status': 'failed', 'result': "Exception in workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43': Exception in transition transition-MessageToDataComponent-1750769557490: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Cannot extract all fields from the input message. Please specify fields to extract.", 'workflow_status': 'failed', 'error': 'Exception in transition transition-MessageToDataComponent-1750769557490: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Cannot extract all fields from the input message. Please specify fields to extract.', 'error_type': 'Exception'}
2025-06-28 00:46:24 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad 
2025-06-28 00:47:08 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 00:47:08 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 00:47:08 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 00:47:08 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 00:47:52 - RedisEventListener - DEBUG - Raw state DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@6__:*', 'channel': b'__keyspace@6__:workflow_state:34e5bf90-efe1-4371-9dc3-103b05ae2016', 'data': b'expired'}
2025-06-28 00:47:52 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@6__:workflow_state:34e5bf90-efe1-4371-9dc3-103b05ae2016'
2025-06-28 00:47:52 - RedisEventListener - DEBUG - Decoded channel: __keyspace@6__:workflow_state:34e5bf90-efe1-4371-9dc3-103b05ae2016
2025-06-28 00:47:52 - RedisEventListener - DEBUG - Extracted key: workflow_state:34e5bf90-efe1-4371-9dc3-103b05ae2016
2025-06-28 00:47:52 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-28 00:47:52 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-28 00:47:52 - RedisEventListener - INFO - Detected expired event for workflow state of workflow: 34e5bf90-efe1-4371-9dc3-103b05ae2016
2025-06-28 00:47:52 - RedisEventListener - INFO - Archiving workflow state for workflow: 34e5bf90-efe1-4371-9dc3-103b05ae2016
2025-06-28 00:47:56 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 00:47:57 - PostgresManager - DEBUG - Inserted new workflow state for correlation_id: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad
2025-06-28 00:47:57 - StateManager - INFO - Archived workflow state to PostgreSQL for workflow ID: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad
2025-06-28 00:48:08 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 00:48:08 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 00:48:09 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 00:48:09 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 00:49:08 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 00:49:08 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 00:49:09 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 00:49:09 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 00:49:46 - RedisEventListener - DEBUG - Raw state DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@6__:*', 'channel': b'__keyspace@6__:workflow_state:b55c2b95-f1c3-4ce4-a6d4-843b3ff367db', 'data': b'expired'}
2025-06-28 00:49:46 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@6__:workflow_state:b55c2b95-f1c3-4ce4-a6d4-843b3ff367db'
2025-06-28 00:49:46 - RedisEventListener - DEBUG - Decoded channel: __keyspace@6__:workflow_state:b55c2b95-f1c3-4ce4-a6d4-843b3ff367db
2025-06-28 00:49:46 - RedisEventListener - DEBUG - Extracted key: workflow_state:b55c2b95-f1c3-4ce4-a6d4-843b3ff367db
2025-06-28 00:49:46 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-28 00:49:46 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-28 00:49:46 - RedisEventListener - INFO - Detected expired event for workflow state of workflow: b55c2b95-f1c3-4ce4-a6d4-843b3ff367db
2025-06-28 00:49:46 - RedisEventListener - INFO - Archiving workflow state for workflow: b55c2b95-f1c3-4ce4-a6d4-843b3ff367db
2025-06-28 00:49:50 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 00:49:51 - PostgresManager - DEBUG - Updated workflow state for correlation_id: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad
2025-06-28 00:49:51 - StateManager - INFO - Archived workflow state to PostgreSQL for workflow ID: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad
2025-06-28 00:50:08 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 00:50:08 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 00:50:09 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 00:50:09 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 00:51:08 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 00:51:08 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 00:51:09 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 00:51:09 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 00:51:21 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-CombineTextComponent-*************', 'data': b'expired'}
2025-06-28 00:51:21 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-CombineTextComponent-*************'
2025-06-28 00:51:21 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-CombineTextComponent-*************
2025-06-28 00:51:21 - RedisEventListener - DEBUG - Extracted key: result:transition-CombineTextComponent-*************
2025-06-28 00:51:21 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-28 00:51:21 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-28 00:51:21 - RedisEventListener - INFO - Detected expired event for result of transition: transition-CombineTextComponent-*************
2025-06-28 00:51:21 - RedisEventListener - INFO - Archiving result for transition: transition-CombineTextComponent-*************
2025-06-28 00:51:21 - StateManager - DEBUG - Attempting to archive result for transition transition-CombineTextComponent-*************
2025-06-28 00:51:22 - StateManager - DEBUG - Provided result: False
2025-06-28 00:51:23 - StateManager - DEBUG - Trying to get result from Redis for transition transition-CombineTextComponent-*************
2025-06-28 00:51:23 - StateManager - DEBUG - No result found in Redis for transition transition-CombineTextComponent-*************
2025-06-28 00:51:23 - StateManager - DEBUG - Trying to get result from memory for transition transition-CombineTextComponent-*************
2025-06-28 00:51:23 - StateManager - DEBUG - Found result in memory for transition transition-CombineTextComponent-*************
2025-06-28 00:51:23 - StateManager - DEBUG - Archiving result to PostgreSQL for transition transition-CombineTextComponent-*************
2025-06-28 00:51:23 - PostgresManager - DEBUG - Attempting to store transition result for transition-CombineTextComponent-************* in correlation 5af9a7b9-62bf-48c0-9b3e-02299ff277ad
2025-06-28 00:51:23 - PostgresManager - DEBUG - Result data type: <class 'dict'>
2025-06-28 00:51:23 - PostgresManager - DEBUG - Result data: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': '2\ncoming'}, 'status': 'completed', 'timestamp': 1751051780.8192708}}
2025-06-28 00:51:26 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 00:51:27 - PostgresManager - DEBUG - Successfully serialized result data to JSON string
2025-06-28 00:51:27 - PostgresManager - DEBUG - Converted to psycopg2 Json object
2025-06-28 00:51:27 - PostgresManager - DEBUG - Inserting new record for transition transition-CombineTextComponent-*************
2025-06-28 00:51:27 - PostgresManager - DEBUG - Inserted new result for transition transition-CombineTextComponent-************* in correlation 5af9a7b9-62bf-48c0-9b3e-02299ff277ad
2025-06-28 00:51:27 - PostgresManager - DEBUG - Successfully stored transition result for transition-CombineTextComponent-*************
2025-06-28 00:51:27 - StateManager - INFO - Archived result for transition transition-CombineTextComponent-************* to PostgreSQL
2025-06-28 00:51:27 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-LoopNode-*************', 'data': b'expired'}
2025-06-28 00:51:27 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-LoopNode-*************'
2025-06-28 00:51:27 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-LoopNode-*************
2025-06-28 00:51:27 - RedisEventListener - DEBUG - Extracted key: result:transition-LoopNode-*************
2025-06-28 00:51:27 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-28 00:51:27 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-28 00:51:27 - RedisEventListener - INFO - Detected expired event for result of transition: transition-LoopNode-*************
2025-06-28 00:51:27 - RedisEventListener - INFO - Archiving result for transition: transition-LoopNode-*************
2025-06-28 00:51:27 - StateManager - DEBUG - Attempting to archive result for transition transition-LoopNode-*************
2025-06-28 00:51:28 - StateManager - DEBUG - Provided result: False
2025-06-28 00:51:28 - StateManager - DEBUG - Trying to get result from Redis for transition transition-LoopNode-*************
2025-06-28 00:51:29 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************
2025-06-28 00:51:29 - StateManager - DEBUG - Trying to get result from memory for transition transition-LoopNode-*************
2025-06-28 00:51:29 - StateManager - DEBUG - Found result in memory for transition transition-LoopNode-*************
2025-06-28 00:51:29 - StateManager - DEBUG - Archiving result to PostgreSQL for transition transition-LoopNode-*************
2025-06-28 00:51:29 - PostgresManager - DEBUG - Attempting to store transition result for transition-LoopNode-************* in correlation 5af9a7b9-62bf-48c0-9b3e-02299ff277ad
2025-06-28 00:51:29 - PostgresManager - DEBUG - Result data type: <class 'dict'>
2025-06-28 00:51:29 - PostgresManager - DEBUG - Result data: {'final_results': ['1\ncoming', '2\ncoming']}
2025-06-28 00:51:32 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 00:51:33 - PostgresManager - DEBUG - Successfully serialized result data to JSON string
2025-06-28 00:51:33 - PostgresManager - DEBUG - Converted to psycopg2 Json object
2025-06-28 00:51:33 - PostgresManager - DEBUG - Inserting new record for transition transition-LoopNode-*************
2025-06-28 00:51:33 - PostgresManager - DEBUG - Inserted new result for transition transition-LoopNode-************* in correlation 5af9a7b9-62bf-48c0-9b3e-02299ff277ad
2025-06-28 00:51:33 - PostgresManager - DEBUG - Successfully stored transition result for transition-LoopNode-*************
2025-06-28 00:51:33 - StateManager - INFO - Archived result for transition transition-LoopNode-************* to PostgreSQL
2025-06-28 00:52:08 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 00:52:08 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 00:52:09 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 00:52:09 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 00:52:31 - RedisEventListener - DEBUG - Raw state DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@6__:*', 'channel': b'__keyspace@6__:workflow_state:e67149c0-0aa9-44f4-88f8-2ac5c230c041', 'data': b'expired'}
2025-06-28 00:52:31 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@6__:workflow_state:e67149c0-0aa9-44f4-88f8-2ac5c230c041'
2025-06-28 00:52:31 - RedisEventListener - DEBUG - Decoded channel: __keyspace@6__:workflow_state:e67149c0-0aa9-44f4-88f8-2ac5c230c041
2025-06-28 00:52:31 - RedisEventListener - DEBUG - Extracted key: workflow_state:e67149c0-0aa9-44f4-88f8-2ac5c230c041
2025-06-28 00:52:31 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-28 00:52:31 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-28 00:52:31 - RedisEventListener - INFO - Detected expired event for workflow state of workflow: e67149c0-0aa9-44f4-88f8-2ac5c230c041
2025-06-28 00:52:31 - RedisEventListener - INFO - Archiving workflow state for workflow: e67149c0-0aa9-44f4-88f8-2ac5c230c041
2025-06-28 00:52:35 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 00:52:36 - PostgresManager - DEBUG - Updated workflow state for correlation_id: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad
2025-06-28 00:52:36 - StateManager - INFO - Archived workflow state to PostgreSQL for workflow ID: 5af9a7b9-62bf-48c0-9b3e-02299ff277ad
2025-06-28 00:53:08 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 00:53:08 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 00:53:09 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 00:53:09 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 00:54:08 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 00:54:08 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 00:54:09 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 00:54:09 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 00:55:08 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 00:55:08 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 00:55:09 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 00:55:09 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 00:55:11 - KafkaWorkflowConsumer - INFO - Consumer task cancelled.
2025-06-28 00:55:11 - MCPToolExecutor - INFO - Result consumer loop cancelled.
2025-06-28 00:55:11 - NodeExecutor - INFO - Result consumer loop cancelled.
2025-06-28 00:55:11 - AgentExecutor - INFO - Result consumer loop cancelled.
2025-06-28 00:55:11 - Main - ERROR - Shutting down due to keyboard interrupt...
2025-06-28 01:00:23 - Main - INFO - Starting Server
2025-06-28 01:00:23 - Main - INFO - Connection at: **************:9092
2025-06-28 01:00:23 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-28 01:00:23 - NodeExecutor - INFO - NodeExecutor initialized.
2025-06-28 01:00:23 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-28 01:00:23 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-28 01:00:23 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 01:00:25 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 01:00:25 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-28 01:00:27 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-28 01:00:29 - PostgresManager - INFO - PostgreSQL connection pool created
2025-06-28 01:00:29 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-06-28 01:00:32 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 01:00:32 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-06-28 01:00:32 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 01:00:34 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 01:00:34 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-28 01:00:36 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-28 01:00:36 - RedisEventListener - INFO - Starting Redis event listener thread
2025-06-28 01:00:36 - RedisEventListener - INFO - Redis event listener started
2025-06-28 01:00:36 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-06-28 01:00:36 - StateManager - DEBUG - Using provided database connections
2025-06-28 01:00:36 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 01:00:36 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 01:00:36 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 01:00:37 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-06-28 01:00:37 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 01:00:37 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 01:00:37 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-06-28 01:00:37 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-06-28 01:00:37 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-06-28 01:00:37 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-06-28 01:00:40 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-06-28 01:00:40 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-06-28 01:00:40 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-06-28 01:01:03 - Main - ERROR - Error occurred: KafkaConnectionError: No connection to node with id 1:Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/main.py", line 37, in start_server
    asyncio.run(consume())
    ~~~~~~~~~~~^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/execution/executor_server_kafka.py", line 941, in consume
    await consumer_instance.start_consumer()
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/execution/executor_server_kafka.py", line 88, in start_consumer
    await self.consumer.start()
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/orchestration-engine-tNJVEdE_-py3.13/lib/python3.13/site-packages/aiokafka/consumer/consumer.py", line 371, in start
    await self._client.bootstrap()
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/orchestration-engine-tNJVEdE_-py3.13/lib/python3.13/site-packages/aiokafka/client.py", line 270, in bootstrap
    self._api_version = await self.check_version()
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/orchestration-engine-tNJVEdE_-py3.13/lib/python3.13/site-packages/aiokafka/client.py", line 563, in check_version
    raise KafkaConnectionError(f"No connection to node with id {node_id}")
aiokafka.errors.KafkaConnectionError: KafkaConnectionError: No connection to node with id 1

2025-06-28 01:01:09 - Main - INFO - Starting Server
2025-06-28 01:01:09 - Main - INFO - Connection at: **************:9092
2025-06-28 01:01:09 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-28 01:01:09 - NodeExecutor - INFO - NodeExecutor initialized.
2025-06-28 01:01:09 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-28 01:01:09 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-28 01:01:09 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 01:01:10 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 01:01:10 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-28 01:01:12 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-28 01:01:14 - PostgresManager - INFO - PostgreSQL connection pool created
2025-06-28 01:01:14 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-06-28 01:01:17 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 01:01:17 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-06-28 01:01:17 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 01:01:19 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 01:01:19 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-28 01:01:21 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-28 01:01:21 - RedisEventListener - INFO - Starting Redis event listener thread
2025-06-28 01:01:21 - RedisEventListener - INFO - Redis event listener started
2025-06-28 01:01:21 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-06-28 01:01:21 - StateManager - DEBUG - Using provided database connections
2025-06-28 01:01:21 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 01:01:21 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 01:01:21 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 01:01:21 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-06-28 01:01:22 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 01:01:22 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 01:01:22 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-06-28 01:01:22 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-06-28 01:01:22 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-06-28 01:01:22 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-06-28 01:01:25 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-06-28 01:01:25 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-06-28 01:01:25 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-06-28 01:01:29 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-06-28 01:01:36 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-06-28 01:01:36 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-06-28 01:01:36 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-06-28 01:01:42 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-06-28 01:01:42 - NodeExecutor - INFO - Background result consumer loop started.
2025-06-28 01:01:42 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-06-28 01:01:49 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-06-28 01:01:49 - AgentExecutor - INFO - Background result consumer loop started.
2025-06-28 01:02:17 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:02:18 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:02:18 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:02:18 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:02:38 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=992
2025-06-28 01:02:38 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1751052758, 'task_type': 'workflow', 'data': {'workflow_id': 'feda07bf-a91e-4004-80cb-72416cdb5a43', 'payload': {'user_dependent_fields': ['start'], 'user_payload_template': {'start': {'value': '1', 'transition_id': 'LoopNode-*************'}}}, 'approval': True, 'user_id': 'c1454e90-09ac-40f2-bde2-833387d7b645'}, 'approval': True}
2025-06-28 01:02:38 - KafkaWorkflowConsumer - INFO - Extracted user_id: c1454e90-09ac-40f2-bde2-833387d7b645 for workflow: feda07bf-a91e-4004-80cb-72416cdb5a43
2025-06-28 01:02:38 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/feda07bf-a91e-4004-80cb-72416cdb5a43
2025-06-28 01:02:39 - WorkflowService - DEBUG - Received response with status code: 200
2025-06-28 01:02:39 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow each loop testing retrieved successfully",
  "workflow": {
    "id": "feda07bf-a91e-4004-80cb-72416cdb5a43",
    "name": "each loop testing",
    "description": "each_loop_testing",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/12293f00-40ea-4c24-b735-9f4686dde518.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/81498d65-d380-4b32-a028-b49018234cb7.json",
    "start_nodes": [
      {
        "field": "start",
        "type": "string",
        "transition_id": "transition-LoopNode-*************"
      }
    ],
    "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645",
    "user_ids": [
      "c1454e90-09ac-40f2-bde2-833387d7b645"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-24T11:13:46.129953",
    "updated_at": "2025-06-27T19:29:59.872424",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      },
      {
        "name": "LoopNode",
        "display_name": "For Each Loop",
        "type": "component",
        "transition_id": "transition-LoopNode-*************"
      },
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-*************"
      }
    ],
    "is_updated": true
  }
}
2025-06-28 01:02:40 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for feda07bf-a91e-4004-80cb-72416cdb5a43 - server_script_path is optional
2025-06-28 01:02:40 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-06-28 01:02:40 - StateManager - DEBUG - Using global database connections from initializer
2025-06-28 01:02:40 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 01:02:40 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 01:02:40 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 01:02:40 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 01:02:40 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 01:02:40 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-06-28 01:02:40 - StateManager - DEBUG - Using provided database connections
2025-06-28 01:02:40 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 01:02:40 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 01:02:40 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 01:02:41 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 01:02:41 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 01:02:41 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-LoopNode-*************']
2025-06-28 01:02:41 - StateManager - DEBUG - Extracted dependencies for transition transition-MergeDataComponent-*************: ['transition-LoopNode-*************']
2025-06-28 01:02:41 - StateManager - INFO - Built dependency map for 3 transitions
2025-06-28 01:02:41 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* depends on: ['transition-LoopNode-*************']
2025-06-28 01:02:41 - StateManager - DEBUG - Transition transition-MergeDataComponent-************* depends on: ['transition-LoopNode-*************']
2025-06-28 01:02:41 - MCPToolExecutor - DEBUG - Set correlation ID to: 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152
2025-06-28 01:02:41 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152 in tool_executor
2025-06-28 01:02:41 - MCPToolExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-06-28 01:02:41 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in tool_executor
2025-06-28 01:02:41 - NodeExecutor - DEBUG - Set correlation ID to: 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152
2025-06-28 01:02:41 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152 in node_executor
2025-06-28 01:02:41 - AgentExecutor - DEBUG - Set correlation ID to: 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152
2025-06-28 01:02:41 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152 in agent_executor
2025-06-28 01:02:41 - AgentExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-06-28 01:02:41 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in agent_executor
2025-06-28 01:02:41 - TransitionHandler - INFO - TransitionHandler initialized
2025-06-28 01:02:41 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152
2025-06-28 01:02:41 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152
2025-06-28 01:02:41 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-06-28 01:02:41 - StateManager - INFO - Workflow initialized with initial transition: transition-LoopNode-*************
2025-06-28 01:02:41 - StateManager - DEBUG - State: pending={'transition-LoopNode-*************'}, waiting=set(), completed=set()
2025-06-28 01:02:41 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-LoopNode-*************
2025-06-28 01:02:41 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-28 01:02:42 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152'
2025-06-28 01:02:43 - RedisManager - DEBUG - Set key 'workflow_state:4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152' with TTL of 600 seconds
2025-06-28 01:02:43 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:02:43 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 01:02:43 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 01:02:43 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-28 01:02:43 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 01:02:43 - StateManager - INFO - Terminated: False
2025-06-28 01:02:43 - StateManager - INFO - Pending transitions (0): []
2025-06-28 01:02:43 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 01:02:43 - StateManager - INFO - Completed transitions (0): []
2025-06-28 01:02:43 - StateManager - INFO - Results stored for 0 transitions
2025-06-28 01:02:43 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 01:02:43 - StateManager - INFO - Workflow status: inactive
2025-06-28 01:02:43 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 01:02:43 - StateManager - INFO - Workflow status: inactive
2025-06-28 01:02:43 - StateManager - INFO - Workflow paused: False
2025-06-28 01:02:43 - StateManager - INFO - ==============================
2025-06-28 01:02:43 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-28 01:02:43 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152):
2025-06-28 01:02:43 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-06-28 01:02:43 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=initial, execution_type=loop)
2025-06-28 01:02:43 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-28 01:02:43 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-28 01:02:43 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-28 01:02:43 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-28 01:02:43 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-28 01:02:43 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-28 01:02:43 - WorkflowUtils - DEBUG - Filtering out field 'iteration_list' with empty collection: []
2025-06-28 01:02:43 - WorkflowUtils - INFO - 🧹 Parameter filtering: 14 → 13 fields (1 null/empty fields removed)
2025-06-28 01:02:43 - TransitionHandler - DEBUG - tool Parameters: {'source_type': 'number_range', 'batch_size': '1', 'start': '1', 'end': '3', 'step': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 01:02:43 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'source_type': 'number_range', 'batch_size': '1', 'start': '1', 'end': '3', 'step': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 01:02:43 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152):
2025-06-28 01:02:43 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-28 01:02:43 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-28 01:02:43 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-28 01:02:43 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-28 01:02:43 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-************* (has final/aggregated indicators)
2025-06-28 01:02:43 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-28 01:02:43 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-28 01:02:43 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: 1
2025-06-28 01:02:43 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-28 01:02:44 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-28 01:02:44 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:02:44 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-28 01:02:44 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0'}
2025-06-28 01:02:44 - StateManager - DEBUG - Stored result for transition current_iteration in memory: 1
2025-06-28 01:02:44 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-28 01:02:45 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-28 01:02:45 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:02:45 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-28 01:02:45 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0'}
2025-06-28 01:02:45 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 241206.38948575, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 2}}
2025-06-28 01:02:45 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 01:02:45 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 01:02:45 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:02:45 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 01:02:45 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_0'}
2025-06-28 01:02:45 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-28 01:02:45 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152):
2025-06-28 01:02:45 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 2, 'workflow_status': 'running'}
2025-06-28 01:02:45 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-28 01:02:45 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 01:02:45 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-28 01:02:45 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 01:02:45 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 01:02:46 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 01:02:46 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-28 01:02:46 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 01:02:46 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 01:02:46 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 241206.38948575, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 2}}
2025-06-28 01:02:46 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 01:02:46 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 01:02:46 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 01:02:46 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 01:02:46 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 241206.38948575, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 2}}
2025-06-28 01:02:46 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 01:02:46 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 01:02:46 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': 1
2025-06-28 01:02:46 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 01:02:46 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 01:02:46 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-28 01:02:46 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-28 01:02:46 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-28 01:02:46 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-28 01:02:46 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-28 01:02:46 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-28 01:02:46 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-28 01:02:46 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-28 01:02:46 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-28 01:02:46 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-28 01:02:46 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 3 fields (10 null/empty fields removed)
2025-06-28 01:02:46 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 01:02:46 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-06-28 01:02:46 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = coming
2025-06-28 01:02:46 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 1, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 01:02:46 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 1, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 01:02:46 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 1, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 01:02:46 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152):
2025-06-28 01:02:46 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 3, 'workflow_status': 'running'}
2025-06-28 01:02:46 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: d651f0da-f1f1-4fae-afac-834e2b985d85) using provided producer.
2025-06-28 01:02:46 - NodeExecutor - DEBUG - Added correlation_id 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152 to payload
2025-06-28 01:02:46 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-28 01:02:46 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 1, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}, 'request_id': 'd651f0da-f1f1-4fae-afac-834e2b985d85', 'correlation_id': '4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-28 01:02:46 - NodeExecutor - DEBUG - Request d651f0da-f1f1-4fae-afac-834e2b985d85 sent successfully using provided producer.
2025-06-28 01:02:46 - NodeExecutor - DEBUG - Waiting indefinitely for result for request d651f0da-f1f1-4fae-afac-834e2b985d85...
2025-06-28 01:02:47 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 992, corr_id: 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152
2025-06-28 01:02:47 - NodeExecutor - DEBUG - Result consumer received message: Offset=805
2025-06-28 01:02:47 - NodeExecutor - DEBUG - Received valid result for request_id d651f0da-f1f1-4fae-afac-834e2b985d85
2025-06-28 01:02:47 - NodeExecutor - INFO - Result received for request d651f0da-f1f1-4fae-afac-834e2b985d85.
2025-06-28 01:02:47 - TransitionHandler - INFO - Execution result from Components executor: "1\ncoming"
2025-06-28 01:02:47 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152):
2025-06-28 01:02:47 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': '1\ncoming', 'status': 'completed', 'sequence': 4, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 01:02:47 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': '1\ncoming'}, 'status': 'completed', 'timestamp': 1751052767.802882}}
2025-06-28 01:02:48 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-28 01:02:48 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-28 01:02:48 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:02:48 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 01:02:48 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'transition-LoopNode-*************', 'transition-CombineTextComponent-*************', 'loop_iteration_0'}
2025-06-28 01:02:48 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-28 01:02:48 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-28 01:02:48 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-28 01:02:48 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 01:02:48 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-28 01:02:48 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-28 01:02:48 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 01:02:48 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 01:02:48 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-28 01:02:48 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.80 seconds
2025-06-28 01:02:48 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152):
2025-06-28 01:02:48 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152, response: {'result': 'Completed transition in 2.80 seconds', 'message': 'Transition completed in 2.80 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 5, 'workflow_status': 'running'}
2025-06-28 01:02:48 - StateManager - DEBUG - Stored result for transition loop_iteration_1 in memory: 2
2025-06-28 01:02:48 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_1
2025-06-28 01:02:49 - RedisManager - DEBUG - Set key 'result:loop_iteration_1' with TTL of 900 seconds
2025-06-28 01:02:49 - StateManager - DEBUG - Stored result for transition loop_iteration_1 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:02:49 - StateManager - INFO - Marked transition loop_iteration_1 as completed (was_pending=False, was_waiting=False)
2025-06-28 01:02:49 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-LoopNode-*************', 'current_iteration', 'transition-CombineTextComponent-*************', 'loop_iteration_1', 'loop_iteration_0'}
2025-06-28 01:02:49 - StateManager - DEBUG - Stored result for transition current_iteration in memory: 2
2025-06-28 01:02:49 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-28 01:02:50 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-28 01:02:50 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:02:50 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-28 01:02:50 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-LoopNode-*************', 'current_iteration', 'transition-CombineTextComponent-*************', 'loop_iteration_1', 'loop_iteration_0'}
2025-06-28 01:02:50 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 2, 'iteration_index': 1, 'iteration_metadata': {'timestamp': 241211.586939958, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 2}}
2025-06-28 01:02:50 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 01:02:51 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 01:02:51 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:02:51 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 01:02:51 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-LoopNode-*************', 'current_iteration', 'transition-CombineTextComponent-*************', 'loop_iteration_1', 'loop_iteration_0'}
2025-06-28 01:02:51 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-28 01:02:51 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 6, corr_id 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152):
2025-06-28 01:02:51 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 6, 'workflow_status': 'running'}
2025-06-28 01:02:51 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-28 01:02:51 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 01:02:51 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-28 01:02:51 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 01:02:51 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 01:02:51 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 01:02:51 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-28 01:02:51 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 01:02:51 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 01:02:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 2, 'iteration_index': 1, 'iteration_metadata': {'timestamp': 241211.586939958, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 2}}
2025-06-28 01:02:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 01:02:51 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 01:02:51 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 01:02:51 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 01:02:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 2, 'iteration_index': 1, 'iteration_metadata': {'timestamp': 241211.586939958, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 2}}
2025-06-28 01:02:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 01:02:51 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 01:02:51 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': 2
2025-06-28 01:02:51 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 01:02:51 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 01:02:51 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-28 01:02:51 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-28 01:02:51 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-28 01:02:51 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-28 01:02:51 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-28 01:02:51 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-28 01:02:51 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-28 01:02:51 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-28 01:02:51 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-28 01:02:51 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-28 01:02:51 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 3 fields (10 null/empty fields removed)
2025-06-28 01:02:51 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 01:02:51 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-06-28 01:02:51 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = coming
2025-06-28 01:02:51 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 2, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 01:02:51 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 2, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 01:02:51 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 2, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 01:02:51 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 7, corr_id 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152):
2025-06-28 01:02:51 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 7, 'workflow_status': 'running'}
2025-06-28 01:02:51 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: fd88d82d-e8f4-472e-bf2d-a23f821d89ff) using provided producer.
2025-06-28 01:02:51 - NodeExecutor - DEBUG - Added correlation_id 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152 to payload
2025-06-28 01:02:51 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-28 01:02:51 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 2, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}, 'request_id': 'fd88d82d-e8f4-472e-bf2d-a23f821d89ff', 'correlation_id': '4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-28 01:02:51 - NodeExecutor - DEBUG - Request fd88d82d-e8f4-472e-bf2d-a23f821d89ff sent successfully using provided producer.
2025-06-28 01:02:51 - NodeExecutor - DEBUG - Waiting indefinitely for result for request fd88d82d-e8f4-472e-bf2d-a23f821d89ff...
2025-06-28 01:02:52 - NodeExecutor - DEBUG - Result consumer received message: Offset=806
2025-06-28 01:02:52 - NodeExecutor - DEBUG - Received valid result for request_id fd88d82d-e8f4-472e-bf2d-a23f821d89ff
2025-06-28 01:02:52 - NodeExecutor - INFO - Result received for request fd88d82d-e8f4-472e-bf2d-a23f821d89ff.
2025-06-28 01:02:52 - TransitionHandler - INFO - Execution result from Components executor: "2\ncoming"
2025-06-28 01:02:52 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 8, corr_id 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152):
2025-06-28 01:02:52 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': '2\ncoming', 'status': 'completed', 'sequence': 8, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 01:02:52 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': '2\ncoming'}, 'status': 'completed', 'timestamp': 1751052772.903796}}
2025-06-28 01:02:53 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-28 01:02:53 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-28 01:02:53 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:02:53 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 01:02:53 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-LoopNode-*************', 'current_iteration', 'transition-CombineTextComponent-*************', 'loop_iteration_1', 'loop_iteration_0'}
2025-06-28 01:02:53 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-28 01:02:53 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-28 01:02:53 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-28 01:02:53 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 01:02:53 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-28 01:02:53 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-28 01:02:53 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 01:02:53 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 01:02:53 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-28 01:02:53 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.65 seconds
2025-06-28 01:02:53 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 9, corr_id 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152):
2025-06-28 01:02:53 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152, response: {'result': 'Completed transition in 2.65 seconds', 'message': 'Transition completed in 2.65 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 9, 'workflow_status': 'running'}
2025-06-28 01:02:53 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 10, corr_id 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152):
2025-06-28 01:02:53 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152, response: {'status': 'loop_completed', 'metadata': {'success': True, 'final_results': ['1\ncoming', '2\ncoming'], 'iteration_count': 2, 'total_iterations': 2}, 'transition_id': 'transition-LoopNode-*************', 'sequence': 10, 'workflow_status': 'running'}
2025-06-28 01:02:53 - TransitionHandler - DEBUG - 🗑️ Unregistered loop executor for transition: transition-LoopNode-*************
2025-06-28 01:02:53 - TransitionHandler - INFO - Execution result from loop executor: {
  "final_results": [
    "1\ncoming",
    "2\ncoming"
  ]
}
2025-06-28 01:02:53 - TransitionHandler - INFO - Checking execution result for errors: {
  "final_results": [
    "1\ncoming",
    "2\ncoming"
  ]
}
2025-06-28 01:02:53 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 11, corr_id 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152):
2025-06-28 01:02:53 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Transition Result received.', 'result': {'final_results': ['1\ncoming', '2\ncoming']}, 'status': 'completed', 'sequence': 11, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 01:02:53 - TransitionHandler - INFO - 🔄 Handling loop completion for transition: transition-LoopNode-*************
2025-06-28 01:02:53 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'final_results': ['1\ncoming', '2\ncoming']}
2025-06-28 01:02:54 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 01:02:54 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 01:02:54 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:02:54 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 01:02:54 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-LoopNode-*************', 'current_iteration', 'transition-CombineTextComponent-*************', 'loop_iteration_1', 'loop_iteration_0'}
2025-06-28 01:02:54 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-28 01:02:54 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-************* (has final/aggregated indicators)
2025-06-28 01:02:54 - TransitionHandler - INFO - 🔍 Auto-detected loop body transitions: ['transition-CombineTextComponent-*************']
2025-06-28 01:02:54 - TransitionHandler - DEBUG - 🔍 Loop completion filtering - Loop config: {'iteration_behavior': 'independent', 'iteration_source': {'number_range': {'start': 1, 'end': 3}, 'step': 1}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}}
2025-06-28 01:02:54 - TransitionHandler - DEBUG - 🔍 Loop body transitions to filter: ['transition-CombineTextComponent-*************']
2025-06-28 01:02:54 - TransitionHandler - DEBUG - 🔍 Output data configs: [{'to_transition_id': 'transition-CombineTextComponent-*************', 'target_node_id': 'Combine Text', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'current_item', 'result_path': 'current_item', 'edge_id': 'reactflow__edge-LoopNode-*************current_item-CombineTextComponent-*************main_input'}]}}, {'to_transition_id': 'transition-MergeDataComponent-*************', 'target_node_id': 'Merge Data', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'final_results', 'result_path': 'final_results', 'edge_id': 'reactflow__edge-LoopNode-*************final_results-MergeDataComponent-*************main_input'}]}}]
2025-06-28 01:02:54 - TransitionHandler - DEBUG - 🚫 Skipping loop body transition 'transition-CombineTextComponent-*************' - should have been executed internally by loop executor
2025-06-28 01:02:54 - TransitionHandler - DEBUG - ✅ Adding exit transition 'transition-MergeDataComponent-*************' to next transitions
2025-06-28 01:02:54 - TransitionHandler - INFO - ✅ Loop completion handled for transition-LoopNode-*************. Next transitions: ['transition-MergeDataComponent-*************']
2025-06-28 01:02:54 - TransitionHandler - INFO - Completed transition transition-LoopNode-************* in 11.38 seconds
2025-06-28 01:02:54 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 12, corr_id 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152):
2025-06-28 01:02:54 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152, response: {'result': 'Completed transition in 11.38 seconds', 'message': 'Transition completed in 11.38 seconds', 'transition_id': 'transition-LoopNode-*************', 'status': 'time_logged', 'sequence': 12, 'workflow_status': 'running'}
2025-06-28 01:02:54 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-MergeDataComponent-*************']]
2025-06-28 01:02:54 - EnhancedWorkflowEngine - INFO - Transition transition-LoopNode-************* completed successfully: 1 next transitions
2025-06-28 01:02:54 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-MergeDataComponent-*************']
2025-06-28 01:02:54 - EnhancedWorkflowEngine - INFO - Adding transition transition-MergeDataComponent-************* to pending (all dependencies met)
2025-06-28 01:02:54 - StateManager - DEBUG - Workflow active: {'transition-MergeDataComponent-*************'}
2025-06-28 01:02:55 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152'
2025-06-28 01:02:55 - RedisManager - DEBUG - Set key 'workflow_state:4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152' with TTL of 600 seconds
2025-06-28 01:02:55 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:02:55 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 01:02:55 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 01:02:55 - StateManager - INFO - Cleared 1 pending transitions: {'transition-MergeDataComponent-*************'}
2025-06-28 01:02:55 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 01:02:55 - StateManager - INFO - Terminated: False
2025-06-28 01:02:55 - StateManager - INFO - Pending transitions (0): []
2025-06-28 01:02:55 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 01:02:55 - StateManager - INFO - Completed transitions (5): ['current_iteration', 'loop_iteration_0', 'loop_iteration_1', 'transition-CombineTextComponent-*************', 'transition-LoopNode-*************']
2025-06-28 01:02:55 - StateManager - INFO - Results stored for 5 transitions
2025-06-28 01:02:55 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 01:02:55 - StateManager - INFO - Workflow status: inactive
2025-06-28 01:02:55 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 01:02:55 - StateManager - INFO - Workflow status: inactive
2025-06-28 01:02:55 - StateManager - INFO - Workflow paused: False
2025-06-28 01:02:55 - StateManager - INFO - ==============================
2025-06-28 01:02:55 - TransitionHandler - INFO - Starting parallel execution of transition: transition-MergeDataComponent-*************
2025-06-28 01:02:55 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 13, corr_id 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152):
2025-06-28 01:02:55 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152, response: {'result': 'Starting execution of transition: transition-MergeDataComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'started', 'sequence': 13, 'workflow_status': 'running'}
2025-06-28 01:02:55 - TransitionHandler - EXECUTE - Transition 'transition-MergeDataComponent-*************' (type=standard, execution_type=Components)
2025-06-28 01:02:55 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 01:02:55 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-MergeDataComponent-*************
2025-06-28 01:02:55 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 01:02:55 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 01:02:56 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 01:02:56 - StateManager - DEBUG - Extracted results for 1 tools in transition transition-LoopNode-*************
2025-06-28 01:02:56 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 01:02:56 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 01:02:56 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_results': {'final_results': ['1\ncoming', '2\ncoming']}
2025-06-28 01:02:56 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-28 01:02:56 - WorkflowUtils - DEBUG - Path tracking - Found handle 'final_results' directly in source_results
2025-06-28 01:02:56 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 01:02:56 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 01:02:56 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_results': {'final_results': ['1\ncoming', '2\ncoming']}
2025-06-28 01:02:56 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-28 01:02:56 - WorkflowUtils - DEBUG - Path tracking - Found handle 'final_results' directly in source_results
2025-06-28 01:02:56 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_results → main_input via path 'final_results': ['1\ncoming', '2\ncoming']
2025-06-28 01:02:56 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 01:02:56 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 01:02:56 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-28 01:02:56 - WorkflowUtils - DEBUG - Filtering out field 'output_key_1' with null/empty value: 
2025-06-28 01:02:56 - WorkflowUtils - DEBUG - Filtering out field 'output_key_2' with null/empty value: 
2025-06-28 01:02:56 - WorkflowUtils - DEBUG - Filtering out field 'output_key_3' with null/empty value: 
2025-06-28 01:02:56 - WorkflowUtils - DEBUG - Filtering out field 'output_key_4' with null/empty value: 
2025-06-28 01:02:56 - WorkflowUtils - DEBUG - Filtering out field 'output_key_5' with null/empty value: 
2025-06-28 01:02:56 - WorkflowUtils - DEBUG - Filtering out field 'output_key_6' with null/empty value: 
2025-06-28 01:02:56 - WorkflowUtils - DEBUG - Filtering out field 'output_key_7' with null/empty value: 
2025-06-28 01:02:56 - WorkflowUtils - DEBUG - Filtering out field 'output_key_8' with null/empty value: 
2025-06-28 01:02:56 - WorkflowUtils - DEBUG - Filtering out field 'output_key_9' with null/empty value: 
2025-06-28 01:02:56 - WorkflowUtils - DEBUG - Filtering out field 'output_key_10' with null/empty value: 
2025-06-28 01:02:56 - WorkflowUtils - DEBUG - Filtering out field 'output_key_11' with null/empty value: 
2025-06-28 01:02:56 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with empty collection: {}
2025-06-28 01:02:56 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with empty collection: {}
2025-06-28 01:02:56 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with empty collection: {}
2025-06-28 01:02:56 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with empty collection: {}
2025-06-28 01:02:56 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with empty collection: {}
2025-06-28 01:02:56 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with empty collection: {}
2025-06-28 01:02:56 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with empty collection: {}
2025-06-28 01:02:56 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with empty collection: {}
2025-06-28 01:02:56 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with empty collection: {}
2025-06-28 01:02:56 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with empty collection: {}
2025-06-28 01:02:56 - WorkflowUtils - INFO - 🧹 Parameter filtering: 24 → 2 fields (22 null/empty fields removed)
2025-06-28 01:02:56 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 01:02:56 - TransitionHandler - DEBUG - 📌 Added static parameter: merge_strategy = Aggregate
2025-06-28 01:02:56 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': ['1\ncoming', '2\ncoming'], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-06-28 01:02:56 - TransitionHandler - DEBUG - tool Parameters: {'main_input': ['1\ncoming', '2\ncoming'], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-06-28 01:02:56 - TransitionHandler - INFO - Invoking tool 'MergeDataComponent' (tool_id: 1) for node 'MergeDataComponent' in transition 'transition-MergeDataComponent-*************' with parameters: {'main_input': ['1\ncoming', '2\ncoming'], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-06-28 01:02:56 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 14, corr_id 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152):
2025-06-28 01:02:56 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'message': 'Connecting to server', 'result': 'Connecting to server MergeDataComponent', 'status': 'connecting', 'sequence': 14, 'workflow_status': 'running'}
2025-06-28 01:02:56 - NodeExecutor - INFO - Executing tool 'MergeDataComponent' via Kafka (request_id: 5e3e1dba-bc05-48a1-b85d-a2b581dcca6e) using provided producer.
2025-06-28 01:02:56 - NodeExecutor - DEBUG - Added correlation_id 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152 to payload
2025-06-28 01:02:56 - NodeExecutor - DEBUG - Added transition_id transition-MergeDataComponent-************* to payload
2025-06-28 01:02:56 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'MergeDataComponent', 'tool_parameters': {'main_input': ['1\ncoming', '2\ncoming'], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}, 'request_id': '5e3e1dba-bc05-48a1-b85d-a2b581dcca6e', 'correlation_id': '4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152', 'transition_id': 'transition-MergeDataComponent-*************'}
2025-06-28 01:02:56 - NodeExecutor - DEBUG - Request 5e3e1dba-bc05-48a1-b85d-a2b581dcca6e sent successfully using provided producer.
2025-06-28 01:02:56 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 5e3e1dba-bc05-48a1-b85d-a2b581dcca6e...
2025-06-28 01:02:56 - NodeExecutor - DEBUG - Result consumer received message: Offset=807
2025-06-28 01:02:56 - NodeExecutor - DEBUG - Received valid result for request_id 5e3e1dba-bc05-48a1-b85d-a2b581dcca6e
2025-06-28 01:02:56 - NodeExecutor - INFO - Result received for request 5e3e1dba-bc05-48a1-b85d-a2b581dcca6e.
2025-06-28 01:02:56 - TransitionHandler - INFO - Execution result from Components executor: [
  "1\ncoming",
  "2\ncoming"
]
2025-06-28 01:02:56 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 15, corr_id 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152):
2025-06-28 01:02:56 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'message': 'Transition Result received.', 'result': ['1\ncoming', '2\ncoming'], 'status': 'completed', 'sequence': 15, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 01:02:56 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in memory: {'MergeDataComponent': {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'result': {'result': ['1\ncoming', '2\ncoming']}, 'status': 'completed', 'timestamp': 1751052776.867976}}
2025-06-28 01:02:57 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-MergeDataComponent-*************'
2025-06-28 01:02:57 - RedisManager - DEBUG - Set key 'result:transition-MergeDataComponent-*************' with TTL of 300 seconds
2025-06-28 01:02:57 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:02:57 - StateManager - INFO - Marked transition transition-MergeDataComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 01:02:57 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-MergeDataComponent-*************', 'transition-LoopNode-*************', 'current_iteration', 'transition-CombineTextComponent-*************', 'loop_iteration_1', 'loop_iteration_0'}
2025-06-28 01:02:57 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-MergeDataComponent-*************
2025-06-28 01:02:57 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'list'>
2025-06-28 01:02:57 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 01:02:57 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'list'>
2025-06-28 01:02:57 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-MergeDataComponent-*************:
2025-06-28 01:02:57 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 01:02:57 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 01:02:57 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-MergeDataComponent-*************, returning empty list
2025-06-28 01:02:57 - TransitionHandler - INFO - Completed transition transition-MergeDataComponent-************* in 2.23 seconds
2025-06-28 01:02:57 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 16, corr_id 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152):
2025-06-28 01:02:57 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152, response: {'result': 'Completed transition in 2.23 seconds', 'message': 'Transition completed in 2.23 seconds', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'time_logged', 'sequence': 16, 'workflow_status': 'running'}
2025-06-28 01:02:57 - EnhancedWorkflowEngine - DEBUG - Results: [[]]
2025-06-28 01:02:57 - StateManager - INFO - Workflow terminated flag set to: True
2025-06-28 01:02:57 - EnhancedWorkflowEngine - INFO - Workflow is marked as terminated, ending workflow execution.
2025-06-28 01:02:57 - StateManager - DEBUG - Workflow active: False (terminated=True, pending=empty, waiting=empty)
2025-06-28 01:02:57 - EnhancedWorkflowEngine - INFO - Workflow execution completed.
2025-06-28 01:02:57 - KafkaWorkflowConsumer - INFO - Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' final status: completed, result: Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' executed successfully.
2025-06-28 01:02:57 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152, response: {'status': 'complete', 'result': "Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' executed successfully.", 'workflow_status': 'completed'}
2025-06-28 01:02:57 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152 
2025-06-28 01:03:18 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:03:18 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:03:18 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:03:18 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:04:18 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:04:18 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:04:18 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:04:18 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:05:18 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:05:18 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:05:18 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:05:18 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:06:12 - KafkaWorkflowConsumer - INFO - Consumer task cancelled.
2025-06-28 01:06:13 - MCPToolExecutor - INFO - Result consumer loop cancelled.
2025-06-28 01:06:13 - AgentExecutor - INFO - Result consumer loop cancelled.
2025-06-28 01:06:13 - NodeExecutor - INFO - Result consumer loop cancelled.
2025-06-28 01:06:13 - Main - ERROR - Shutting down due to keyboard interrupt...
2025-06-28 01:06:15 - Main - INFO - Starting Server
2025-06-28 01:06:15 - Main - INFO - Connection at: **************:9092
2025-06-28 01:06:15 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-28 01:06:15 - NodeExecutor - INFO - NodeExecutor initialized.
2025-06-28 01:06:15 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-28 01:06:15 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-28 01:06:15 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 01:06:17 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 01:06:17 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-28 01:06:18 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-28 01:06:20 - PostgresManager - INFO - PostgreSQL connection pool created
2025-06-28 01:06:20 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-06-28 01:06:23 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 01:06:23 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-06-28 01:06:23 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 01:06:25 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 01:06:25 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-28 01:06:27 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-28 01:06:27 - RedisEventListener - INFO - Starting Redis event listener thread
2025-06-28 01:06:27 - RedisEventListener - INFO - Redis event listener started
2025-06-28 01:06:27 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-06-28 01:06:27 - StateManager - DEBUG - Using provided database connections
2025-06-28 01:06:27 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 01:06:27 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 01:06:27 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 01:06:27 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-06-28 01:06:27 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 01:06:27 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 01:06:27 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-06-28 01:06:27 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-06-28 01:06:28 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-06-28 01:06:28 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-06-28 01:06:30 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-06-28 01:06:30 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-06-28 01:06:30 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-06-28 01:06:44 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-06-28 01:06:51 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-06-28 01:06:51 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-06-28 01:06:51 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-06-28 01:06:57 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-06-28 01:06:57 - NodeExecutor - INFO - Background result consumer loop started.
2025-06-28 01:06:57 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-06-28 01:07:04 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-06-28 01:07:04 - AgentExecutor - INFO - Background result consumer loop started.
2025-06-28 01:07:23 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:07:24 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:07:24 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:07:24 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:07:53 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-CombineTextComponent-*************', 'data': b'expired'}
2025-06-28 01:07:53 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-CombineTextComponent-*************'
2025-06-28 01:07:53 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-CombineTextComponent-*************
2025-06-28 01:07:53 - RedisEventListener - DEBUG - Extracted key: result:transition-CombineTextComponent-*************
2025-06-28 01:07:53 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-28 01:07:53 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-28 01:07:53 - RedisEventListener - INFO - Detected expired event for result of transition: transition-CombineTextComponent-*************
2025-06-28 01:07:53 - RedisEventListener - INFO - Archiving result for transition: transition-CombineTextComponent-*************
2025-06-28 01:07:53 - StateManager - DEBUG - Attempting to archive result for transition transition-CombineTextComponent-*************
2025-06-28 01:07:54 - StateManager - DEBUG - Provided result: False
2025-06-28 01:07:55 - StateManager - DEBUG - Trying to get result from Redis for transition transition-CombineTextComponent-*************
2025-06-28 01:07:55 - StateManager - DEBUG - No result found in Redis for transition transition-CombineTextComponent-*************
2025-06-28 01:07:55 - StateManager - DEBUG - Trying to get result from memory for transition transition-CombineTextComponent-*************
2025-06-28 01:07:55 - StateManager - DEBUG - No result found in memory for transition transition-CombineTextComponent-*************
2025-06-28 01:07:55 - StateManager - DEBUG - Available transition results in memory: []
2025-06-28 01:07:55 - StateManager - WARNING - No result found to archive for transition transition-CombineTextComponent-*************
2025-06-28 01:07:55 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-LoopNode-*************', 'data': b'expired'}
2025-06-28 01:07:55 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-LoopNode-*************'
2025-06-28 01:07:55 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-LoopNode-*************
2025-06-28 01:07:55 - RedisEventListener - DEBUG - Extracted key: result:transition-LoopNode-*************
2025-06-28 01:07:55 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-28 01:07:55 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-28 01:07:55 - RedisEventListener - INFO - Detected expired event for result of transition: transition-LoopNode-*************
2025-06-28 01:07:55 - RedisEventListener - INFO - Archiving result for transition: transition-LoopNode-*************
2025-06-28 01:07:55 - StateManager - DEBUG - Attempting to archive result for transition transition-LoopNode-*************
2025-06-28 01:07:56 - StateManager - DEBUG - Provided result: False
2025-06-28 01:07:56 - StateManager - DEBUG - Trying to get result from Redis for transition transition-LoopNode-*************
2025-06-28 01:07:57 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************
2025-06-28 01:07:57 - StateManager - DEBUG - Trying to get result from memory for transition transition-LoopNode-*************
2025-06-28 01:07:57 - StateManager - DEBUG - No result found in memory for transition transition-LoopNode-*************
2025-06-28 01:07:57 - StateManager - DEBUG - Available transition results in memory: []
2025-06-28 01:07:57 - StateManager - WARNING - No result found to archive for transition transition-LoopNode-*************
2025-06-28 01:07:57 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-MergeDataComponent-*************', 'data': b'expired'}
2025-06-28 01:07:57 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-MergeDataComponent-*************'
2025-06-28 01:07:57 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-MergeDataComponent-*************
2025-06-28 01:07:57 - RedisEventListener - DEBUG - Extracted key: result:transition-MergeDataComponent-*************
2025-06-28 01:07:57 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-28 01:07:57 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-28 01:07:57 - RedisEventListener - INFO - Detected expired event for result of transition: transition-MergeDataComponent-*************
2025-06-28 01:07:57 - RedisEventListener - INFO - Archiving result for transition: transition-MergeDataComponent-*************
2025-06-28 01:07:57 - StateManager - DEBUG - Attempting to archive result for transition transition-MergeDataComponent-*************
2025-06-28 01:07:58 - StateManager - DEBUG - Provided result: False
2025-06-28 01:07:58 - StateManager - DEBUG - Trying to get result from Redis for transition transition-MergeDataComponent-*************
2025-06-28 01:07:59 - StateManager - DEBUG - No result found in Redis for transition transition-MergeDataComponent-*************
2025-06-28 01:07:59 - StateManager - DEBUG - Trying to get result from memory for transition transition-MergeDataComponent-*************
2025-06-28 01:07:59 - StateManager - DEBUG - No result found in memory for transition transition-MergeDataComponent-*************
2025-06-28 01:07:59 - StateManager - DEBUG - Available transition results in memory: []
2025-06-28 01:07:59 - StateManager - WARNING - No result found to archive for transition transition-MergeDataComponent-*************
2025-06-28 01:08:04 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=993
2025-06-28 01:08:04 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1751053084, 'task_type': 'workflow', 'data': {'workflow_id': 'feda07bf-a91e-4004-80cb-72416cdb5a43', 'payload': {'user_dependent_fields': ['start'], 'user_payload_template': {'start': {'value': '1', 'transition_id': 'LoopNode-*************'}}}, 'approval': True, 'user_id': 'c1454e90-09ac-40f2-bde2-833387d7b645'}, 'approval': True}
2025-06-28 01:08:04 - KafkaWorkflowConsumer - INFO - Extracted user_id: c1454e90-09ac-40f2-bde2-833387d7b645 for workflow: feda07bf-a91e-4004-80cb-72416cdb5a43
2025-06-28 01:08:04 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/feda07bf-a91e-4004-80cb-72416cdb5a43
2025-06-28 01:08:04 - WorkflowService - DEBUG - Received response with status code: 200
2025-06-28 01:08:04 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow each loop testing retrieved successfully",
  "workflow": {
    "id": "feda07bf-a91e-4004-80cb-72416cdb5a43",
    "name": "each loop testing",
    "description": "each_loop_testing",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/12293f00-40ea-4c24-b735-9f4686dde518.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/81498d65-d380-4b32-a028-b49018234cb7.json",
    "start_nodes": [
      {
        "field": "start",
        "type": "string",
        "transition_id": "transition-LoopNode-*************"
      }
    ],
    "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645",
    "user_ids": [
      "c1454e90-09ac-40f2-bde2-833387d7b645"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-24T11:13:46.129953",
    "updated_at": "2025-06-27T19:29:59.872424",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      },
      {
        "name": "LoopNode",
        "display_name": "For Each Loop",
        "type": "component",
        "transition_id": "transition-LoopNode-*************"
      },
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-*************"
      }
    ],
    "is_updated": true
  }
}
2025-06-28 01:08:04 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for feda07bf-a91e-4004-80cb-72416cdb5a43 - server_script_path is optional
2025-06-28 01:08:04 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-06-28 01:08:04 - StateManager - DEBUG - Using global database connections from initializer
2025-06-28 01:08:04 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 01:08:04 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 01:08:04 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 01:08:05 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 01:08:05 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 01:08:05 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-06-28 01:08:05 - StateManager - DEBUG - Using provided database connections
2025-06-28 01:08:05 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 01:08:05 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 01:08:05 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 01:08:06 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 01:08:06 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 01:08:06 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-LoopNode-*************']
2025-06-28 01:08:06 - StateManager - DEBUG - Extracted dependencies for transition transition-MergeDataComponent-*************: ['transition-LoopNode-*************']
2025-06-28 01:08:06 - StateManager - INFO - Built dependency map for 3 transitions
2025-06-28 01:08:06 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* depends on: ['transition-LoopNode-*************']
2025-06-28 01:08:06 - StateManager - DEBUG - Transition transition-MergeDataComponent-************* depends on: ['transition-LoopNode-*************']
2025-06-28 01:08:06 - MCPToolExecutor - DEBUG - Set correlation ID to: ef594c98-6738-4c5c-b073-6c515ed89d02
2025-06-28 01:08:06 - EnhancedWorkflowEngine - DEBUG - Set correlation_id ef594c98-6738-4c5c-b073-6c515ed89d02 in tool_executor
2025-06-28 01:08:06 - MCPToolExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-06-28 01:08:06 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in tool_executor
2025-06-28 01:08:06 - NodeExecutor - DEBUG - Set correlation ID to: ef594c98-6738-4c5c-b073-6c515ed89d02
2025-06-28 01:08:06 - EnhancedWorkflowEngine - DEBUG - Set correlation_id ef594c98-6738-4c5c-b073-6c515ed89d02 in node_executor
2025-06-28 01:08:06 - AgentExecutor - DEBUG - Set correlation ID to: ef594c98-6738-4c5c-b073-6c515ed89d02
2025-06-28 01:08:06 - EnhancedWorkflowEngine - DEBUG - Set correlation_id ef594c98-6738-4c5c-b073-6c515ed89d02 in agent_executor
2025-06-28 01:08:06 - AgentExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-06-28 01:08:06 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in agent_executor
2025-06-28 01:08:06 - TransitionHandler - INFO - TransitionHandler initialized
2025-06-28 01:08:06 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: ef594c98-6738-4c5c-b073-6c515ed89d02
2025-06-28 01:08:06 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: ef594c98-6738-4c5c-b073-6c515ed89d02
2025-06-28 01:08:06 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: ef594c98-6738-4c5c-b073-6c515ed89d02, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-06-28 01:08:06 - StateManager - INFO - Workflow initialized with initial transition: transition-LoopNode-*************
2025-06-28 01:08:06 - StateManager - DEBUG - State: pending={'transition-LoopNode-*************'}, waiting=set(), completed=set()
2025-06-28 01:08:06 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-LoopNode-*************
2025-06-28 01:08:06 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-28 01:08:07 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:ef594c98-6738-4c5c-b073-6c515ed89d02'
2025-06-28 01:08:07 - RedisManager - DEBUG - Set key 'workflow_state:ef594c98-6738-4c5c-b073-6c515ed89d02' with TTL of 600 seconds
2025-06-28 01:08:07 - StateManager - INFO - Workflow state saved to Redis for workflow ID: ef594c98-6738-4c5c-b073-6c515ed89d02. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:08:07 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 01:08:07 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 01:08:07 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-28 01:08:07 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 01:08:07 - StateManager - INFO - Terminated: False
2025-06-28 01:08:07 - StateManager - INFO - Pending transitions (0): []
2025-06-28 01:08:07 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 01:08:07 - StateManager - INFO - Completed transitions (0): []
2025-06-28 01:08:07 - StateManager - INFO - Results stored for 0 transitions
2025-06-28 01:08:07 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 01:08:07 - StateManager - INFO - Workflow status: inactive
2025-06-28 01:08:07 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 01:08:07 - StateManager - INFO - Workflow status: inactive
2025-06-28 01:08:07 - StateManager - INFO - Workflow paused: False
2025-06-28 01:08:07 - StateManager - INFO - ==============================
2025-06-28 01:08:07 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-28 01:08:07 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id ef594c98-6738-4c5c-b073-6c515ed89d02):
2025-06-28 01:08:07 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: ef594c98-6738-4c5c-b073-6c515ed89d02, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-06-28 01:08:07 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=initial, execution_type=loop)
2025-06-28 01:08:07 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-28 01:08:07 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-28 01:08:07 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-28 01:08:07 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-28 01:08:07 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-28 01:08:07 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-28 01:08:07 - WorkflowUtils - DEBUG - Filtering out field 'iteration_list' with empty collection: []
2025-06-28 01:08:07 - WorkflowUtils - INFO - 🧹 Parameter filtering: 14 → 13 fields (1 null/empty fields removed)
2025-06-28 01:08:07 - TransitionHandler - DEBUG - tool Parameters: {'source_type': 'number_range', 'batch_size': '1', 'start': '1', 'end': '3', 'step': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 01:08:07 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'source_type': 'number_range', 'batch_size': '1', 'start': '1', 'end': '3', 'step': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 01:08:07 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id ef594c98-6738-4c5c-b073-6c515ed89d02):
2025-06-28 01:08:07 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: ef594c98-6738-4c5c-b073-6c515ed89d02, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-28 01:08:07 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-28 01:08:07 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-28 01:08:07 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-28 01:08:07 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-************* (has final/aggregated indicators)
2025-06-28 01:08:07 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-28 01:08:07 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-28 01:08:07 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: 1
2025-06-28 01:08:07 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-28 01:08:08 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-28 01:08:08 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:08:08 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-28 01:08:08 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0'}
2025-06-28 01:08:08 - StateManager - DEBUG - Stored result for transition current_iteration in memory: 1
2025-06-28 01:08:08 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-28 01:08:09 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-28 01:08:09 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:08:09 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-28 01:08:09 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0'}
2025-06-28 01:08:09 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 241530.646167875, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 01:08:09 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 01:08:10 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 01:08:10 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:08:10 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 01:08:10 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_0'}
2025-06-28 01:08:10 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-28 01:08:10 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id ef594c98-6738-4c5c-b073-6c515ed89d02):
2025-06-28 01:08:10 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: ef594c98-6738-4c5c-b073-6c515ed89d02, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 2, 'workflow_status': 'running'}
2025-06-28 01:08:10 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-28 01:08:10 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 01:08:10 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-28 01:08:10 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 01:08:10 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 01:08:10 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 01:08:10 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-28 01:08:10 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 01:08:10 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 01:08:10 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 241530.646167875, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 01:08:10 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 01:08:10 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 01:08:10 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 01:08:10 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 01:08:10 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 241530.646167875, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 01:08:10 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 01:08:10 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 01:08:10 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': 1
2025-06-28 01:08:10 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 01:08:10 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 01:08:10 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-28 01:08:10 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-28 01:08:10 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-28 01:08:10 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-28 01:08:10 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-28 01:08:10 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-28 01:08:10 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-28 01:08:10 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-28 01:08:10 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-28 01:08:10 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-28 01:08:10 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 3 fields (10 null/empty fields removed)
2025-06-28 01:08:10 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 01:08:10 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-06-28 01:08:10 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = coming
2025-06-28 01:08:10 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 1, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 01:08:10 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 1, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 01:08:10 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 1, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 01:08:10 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id ef594c98-6738-4c5c-b073-6c515ed89d02):
2025-06-28 01:08:10 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: ef594c98-6738-4c5c-b073-6c515ed89d02, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 3, 'workflow_status': 'running'}
2025-06-28 01:08:10 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 1a236a8c-fa0a-4654-bfb1-88ac952b9bf8) using provided producer.
2025-06-28 01:08:10 - NodeExecutor - DEBUG - Added correlation_id ef594c98-6738-4c5c-b073-6c515ed89d02 to payload
2025-06-28 01:08:10 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-28 01:08:10 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 1, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}, 'request_id': '1a236a8c-fa0a-4654-bfb1-88ac952b9bf8', 'correlation_id': 'ef594c98-6738-4c5c-b073-6c515ed89d02', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-28 01:08:10 - NodeExecutor - DEBUG - Request 1a236a8c-fa0a-4654-bfb1-88ac952b9bf8 sent successfully using provided producer.
2025-06-28 01:08:10 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 1a236a8c-fa0a-4654-bfb1-88ac952b9bf8...
2025-06-28 01:08:10 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 993, corr_id: ef594c98-6738-4c5c-b073-6c515ed89d02
2025-06-28 01:08:12 - NodeExecutor - DEBUG - Result consumer received message: Offset=808
2025-06-28 01:08:12 - NodeExecutor - DEBUG - Received valid result for request_id 1a236a8c-fa0a-4654-bfb1-88ac952b9bf8
2025-06-28 01:08:12 - NodeExecutor - INFO - Result received for request 1a236a8c-fa0a-4654-bfb1-88ac952b9bf8.
2025-06-28 01:08:12 - TransitionHandler - INFO - Execution result from Components executor: "1\ncoming"
2025-06-28 01:08:12 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id ef594c98-6738-4c5c-b073-6c515ed89d02):
2025-06-28 01:08:12 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: ef594c98-6738-4c5c-b073-6c515ed89d02, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': '1\ncoming', 'status': 'completed', 'sequence': 4, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 01:08:12 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': '1\ncoming'}, 'status': 'completed', 'timestamp': 1751053092.158092}}
2025-06-28 01:08:12 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-28 01:08:12 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-28 01:08:12 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:08:12 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 01:08:12 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_0'}
2025-06-28 01:08:12 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-28 01:08:12 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-28 01:08:12 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-28 01:08:12 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 01:08:12 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-28 01:08:12 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-28 01:08:12 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 01:08:12 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 01:08:12 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-28 01:08:12 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.87 seconds
2025-06-28 01:08:12 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id ef594c98-6738-4c5c-b073-6c515ed89d02):
2025-06-28 01:08:12 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: ef594c98-6738-4c5c-b073-6c515ed89d02, response: {'result': 'Completed transition in 2.87 seconds', 'message': 'Transition completed in 2.87 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 5, 'workflow_status': 'running'}
2025-06-28 01:08:12 - StateManager - DEBUG - Stored result for transition loop_iteration_1 in memory: 2
2025-06-28 01:08:13 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_1
2025-06-28 01:08:13 - RedisManager - DEBUG - Set key 'result:loop_iteration_1' with TTL of 900 seconds
2025-06-28 01:08:13 - StateManager - DEBUG - Stored result for transition loop_iteration_1 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:08:13 - StateManager - INFO - Marked transition loop_iteration_1 as completed (was_pending=False, was_waiting=False)
2025-06-28 01:08:13 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'loop_iteration_1', 'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_0'}
2025-06-28 01:08:13 - StateManager - DEBUG - Stored result for transition current_iteration in memory: 2
2025-06-28 01:08:14 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-28 01:08:14 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-28 01:08:14 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:08:14 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-28 01:08:14 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'loop_iteration_1', 'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_0'}
2025-06-28 01:08:14 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 2, 'iteration_index': 1, 'iteration_metadata': {'timestamp': 241536.18093725, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 01:08:15 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 01:08:15 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 01:08:15 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:08:15 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 01:08:15 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'loop_iteration_1', 'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_0'}
2025-06-28 01:08:15 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-28 01:08:15 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 6, corr_id ef594c98-6738-4c5c-b073-6c515ed89d02):
2025-06-28 01:08:15 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: ef594c98-6738-4c5c-b073-6c515ed89d02, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 6, 'workflow_status': 'running'}
2025-06-28 01:08:15 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-28 01:08:15 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 01:08:15 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-28 01:08:15 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 01:08:15 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 01:08:16 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 01:08:16 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-28 01:08:16 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 01:08:16 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 01:08:16 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 2, 'iteration_index': 1, 'iteration_metadata': {'timestamp': 241536.18093725, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 01:08:16 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 01:08:16 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 01:08:16 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 01:08:16 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 01:08:16 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 2, 'iteration_index': 1, 'iteration_metadata': {'timestamp': 241536.18093725, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 01:08:16 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 01:08:16 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 01:08:16 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': 2
2025-06-28 01:08:16 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 01:08:16 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 01:08:16 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-28 01:08:16 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-28 01:08:16 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-28 01:08:16 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-28 01:08:16 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-28 01:08:16 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-28 01:08:16 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-28 01:08:16 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-28 01:08:16 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-28 01:08:16 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-28 01:08:16 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 3 fields (10 null/empty fields removed)
2025-06-28 01:08:16 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 01:08:16 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-06-28 01:08:16 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = coming
2025-06-28 01:08:16 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 2, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 01:08:16 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 2, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 01:08:16 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 2, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 01:08:16 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 7, corr_id ef594c98-6738-4c5c-b073-6c515ed89d02):
2025-06-28 01:08:16 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: ef594c98-6738-4c5c-b073-6c515ed89d02, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 7, 'workflow_status': 'running'}
2025-06-28 01:08:16 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 512e6f2b-4442-40b4-bbdc-3c33aeb7481b) using provided producer.
2025-06-28 01:08:16 - NodeExecutor - DEBUG - Added correlation_id ef594c98-6738-4c5c-b073-6c515ed89d02 to payload
2025-06-28 01:08:16 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-28 01:08:16 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 2, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}, 'request_id': '512e6f2b-4442-40b4-bbdc-3c33aeb7481b', 'correlation_id': 'ef594c98-6738-4c5c-b073-6c515ed89d02', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-28 01:08:16 - NodeExecutor - DEBUG - Request 512e6f2b-4442-40b4-bbdc-3c33aeb7481b sent successfully using provided producer.
2025-06-28 01:08:16 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 512e6f2b-4442-40b4-bbdc-3c33aeb7481b...
2025-06-28 01:08:17 - NodeExecutor - DEBUG - Result consumer received message: Offset=809
2025-06-28 01:08:17 - NodeExecutor - DEBUG - Received valid result for request_id 512e6f2b-4442-40b4-bbdc-3c33aeb7481b
2025-06-28 01:08:17 - NodeExecutor - INFO - Result received for request 512e6f2b-4442-40b4-bbdc-3c33aeb7481b.
2025-06-28 01:08:17 - TransitionHandler - INFO - Execution result from Components executor: "2\ncoming"
2025-06-28 01:08:17 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 8, corr_id ef594c98-6738-4c5c-b073-6c515ed89d02):
2025-06-28 01:08:17 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: ef594c98-6738-4c5c-b073-6c515ed89d02, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': '2\ncoming', 'status': 'completed', 'sequence': 8, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 01:08:17 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': '2\ncoming'}, 'status': 'completed', 'timestamp': 1751053097.2087228}}
2025-06-28 01:08:17 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-28 01:08:18 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-28 01:08:18 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:08:18 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 01:08:18 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'loop_iteration_1', 'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_0'}
2025-06-28 01:08:18 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-28 01:08:18 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-28 01:08:18 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-28 01:08:18 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 01:08:18 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-28 01:08:18 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-28 01:08:18 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 01:08:18 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 01:08:18 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-28 01:08:18 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.28 seconds
2025-06-28 01:08:18 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 9, corr_id ef594c98-6738-4c5c-b073-6c515ed89d02):
2025-06-28 01:08:18 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: ef594c98-6738-4c5c-b073-6c515ed89d02, response: {'result': 'Completed transition in 2.28 seconds', 'message': 'Transition completed in 2.28 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 9, 'workflow_status': 'running'}
2025-06-28 01:08:18 - StateManager - DEBUG - Stored result for transition loop_iteration_2 in memory: 3
2025-06-28 01:08:18 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_2
2025-06-28 01:08:18 - RedisManager - DEBUG - Set key 'result:loop_iteration_2' with TTL of 900 seconds
2025-06-28 01:08:18 - StateManager - DEBUG - Stored result for transition loop_iteration_2 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:08:18 - StateManager - INFO - Marked transition loop_iteration_2 as completed (was_pending=False, was_waiting=False)
2025-06-28 01:08:18 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'loop_iteration_2', 'loop_iteration_1', 'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_0'}
2025-06-28 01:08:18 - StateManager - DEBUG - Stored result for transition current_iteration in memory: 3
2025-06-28 01:08:19 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-28 01:08:19 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-28 01:08:19 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:08:19 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-28 01:08:19 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'loop_iteration_2', 'loop_iteration_1', 'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_0'}
2025-06-28 01:08:19 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 3, 'iteration_index': 2, 'iteration_metadata': {'timestamp': 241541.169918041, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 01:08:20 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 01:08:20 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 01:08:20 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:08:20 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 01:08:20 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'loop_iteration_2', 'loop_iteration_1', 'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_0'}
2025-06-28 01:08:20 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-28 01:08:20 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 10, corr_id ef594c98-6738-4c5c-b073-6c515ed89d02):
2025-06-28 01:08:20 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: ef594c98-6738-4c5c-b073-6c515ed89d02, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 10, 'workflow_status': 'running'}
2025-06-28 01:08:20 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-28 01:08:20 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 01:08:20 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-28 01:08:20 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 01:08:20 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 01:08:21 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 01:08:21 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-28 01:08:21 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 01:08:21 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 01:08:21 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 3, 'iteration_index': 2, 'iteration_metadata': {'timestamp': 241541.169918041, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 01:08:21 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 01:08:21 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 01:08:21 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 01:08:21 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 01:08:21 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 3, 'iteration_index': 2, 'iteration_metadata': {'timestamp': 241541.169918041, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 01:08:21 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 01:08:21 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 01:08:21 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': 3
2025-06-28 01:08:21 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 01:08:21 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 01:08:21 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-28 01:08:21 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-28 01:08:21 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-28 01:08:21 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-28 01:08:21 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-28 01:08:21 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-28 01:08:21 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-28 01:08:21 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-28 01:08:21 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-28 01:08:21 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-28 01:08:21 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 3 fields (10 null/empty fields removed)
2025-06-28 01:08:21 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 01:08:21 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-06-28 01:08:21 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = coming
2025-06-28 01:08:21 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 3, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 01:08:21 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 3, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 01:08:21 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 3, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 01:08:21 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 11, corr_id ef594c98-6738-4c5c-b073-6c515ed89d02):
2025-06-28 01:08:21 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: ef594c98-6738-4c5c-b073-6c515ed89d02, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 11, 'workflow_status': 'running'}
2025-06-28 01:08:21 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: dd55bd37-a4e5-4106-884b-349851a47ff9) using provided producer.
2025-06-28 01:08:21 - NodeExecutor - DEBUG - Added correlation_id ef594c98-6738-4c5c-b073-6c515ed89d02 to payload
2025-06-28 01:08:21 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-28 01:08:21 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 3, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}, 'request_id': 'dd55bd37-a4e5-4106-884b-349851a47ff9', 'correlation_id': 'ef594c98-6738-4c5c-b073-6c515ed89d02', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-28 01:08:21 - NodeExecutor - DEBUG - Request dd55bd37-a4e5-4106-884b-349851a47ff9 sent successfully using provided producer.
2025-06-28 01:08:21 - NodeExecutor - DEBUG - Waiting indefinitely for result for request dd55bd37-a4e5-4106-884b-349851a47ff9...
2025-06-28 01:08:22 - NodeExecutor - DEBUG - Result consumer received message: Offset=810
2025-06-28 01:08:22 - NodeExecutor - DEBUG - Received valid result for request_id dd55bd37-a4e5-4106-884b-349851a47ff9
2025-06-28 01:08:22 - NodeExecutor - INFO - Result received for request dd55bd37-a4e5-4106-884b-349851a47ff9.
2025-06-28 01:08:22 - TransitionHandler - INFO - Execution result from Components executor: "3\ncoming"
2025-06-28 01:08:22 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 12, corr_id ef594c98-6738-4c5c-b073-6c515ed89d02):
2025-06-28 01:08:22 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: ef594c98-6738-4c5c-b073-6c515ed89d02, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': '3\ncoming', 'status': 'completed', 'sequence': 12, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 01:08:22 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': '3\ncoming'}, 'status': 'completed', 'timestamp': 1751053102.3294022}}
2025-06-28 01:08:22 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-28 01:08:23 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-28 01:08:23 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:08:23 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 01:08:23 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'loop_iteration_2', 'loop_iteration_1', 'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_0'}
2025-06-28 01:08:23 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-28 01:08:23 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-28 01:08:23 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-28 01:08:23 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 01:08:23 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-28 01:08:23 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-28 01:08:23 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 01:08:23 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 01:08:23 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-28 01:08:23 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.47 seconds
2025-06-28 01:08:23 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 13, corr_id ef594c98-6738-4c5c-b073-6c515ed89d02):
2025-06-28 01:08:23 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: ef594c98-6738-4c5c-b073-6c515ed89d02, response: {'result': 'Completed transition in 2.47 seconds', 'message': 'Transition completed in 2.47 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 13, 'workflow_status': 'running'}
2025-06-28 01:08:23 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 14, corr_id ef594c98-6738-4c5c-b073-6c515ed89d02):
2025-06-28 01:08:23 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: ef594c98-6738-4c5c-b073-6c515ed89d02, response: {'status': 'loop_completed', 'metadata': {'success': True, 'final_results': ['1\ncoming', '2\ncoming', '3\ncoming'], 'iteration_count': 3, 'total_iterations': 3}, 'transition_id': 'transition-LoopNode-*************', 'sequence': 14, 'workflow_status': 'running'}
2025-06-28 01:08:23 - TransitionHandler - DEBUG - 🗑️ Unregistered loop executor for transition: transition-LoopNode-*************
2025-06-28 01:08:23 - TransitionHandler - INFO - Execution result from loop executor: {
  "final_results": [
    "1\ncoming",
    "2\ncoming",
    "3\ncoming"
  ]
}
2025-06-28 01:08:23 - TransitionHandler - INFO - Checking execution result for errors: {
  "final_results": [
    "1\ncoming",
    "2\ncoming",
    "3\ncoming"
  ]
}
2025-06-28 01:08:23 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 15, corr_id ef594c98-6738-4c5c-b073-6c515ed89d02):
2025-06-28 01:08:23 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: ef594c98-6738-4c5c-b073-6c515ed89d02, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Transition Result received.', 'result': {'final_results': ['1\ncoming', '2\ncoming', '3\ncoming']}, 'status': 'completed', 'sequence': 15, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 01:08:23 - TransitionHandler - INFO - 🔄 Handling loop completion for transition: transition-LoopNode-*************
2025-06-28 01:08:23 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'final_results': ['1\ncoming', '2\ncoming', '3\ncoming']}
2025-06-28 01:08:23 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 01:08:23 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:08:24 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 01:08:24 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:08:24 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 01:08:24 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'loop_iteration_2', 'loop_iteration_1', 'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_0'}
2025-06-28 01:08:24 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-28 01:08:24 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-************* (has final/aggregated indicators)
2025-06-28 01:08:24 - TransitionHandler - INFO - 🔍 Auto-detected loop body transitions: ['transition-CombineTextComponent-*************']
2025-06-28 01:08:24 - TransitionHandler - DEBUG - 🔍 Loop completion filtering - Loop config: {'iteration_behavior': 'independent', 'iteration_source': {'number_range': {'start': 1, 'end': 3}, 'step': 1}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}}
2025-06-28 01:08:24 - TransitionHandler - DEBUG - 🔍 Loop body transitions to filter: ['transition-CombineTextComponent-*************']
2025-06-28 01:08:24 - TransitionHandler - DEBUG - 🔍 Output data configs: [{'to_transition_id': 'transition-CombineTextComponent-*************', 'target_node_id': 'Combine Text', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'current_item', 'result_path': 'current_item', 'edge_id': 'reactflow__edge-LoopNode-*************current_item-CombineTextComponent-*************main_input'}]}}, {'to_transition_id': 'transition-MergeDataComponent-*************', 'target_node_id': 'Merge Data', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'final_results', 'result_path': 'final_results', 'edge_id': 'reactflow__edge-LoopNode-*************final_results-MergeDataComponent-*************main_input'}]}}]
2025-06-28 01:08:24 - TransitionHandler - DEBUG - 🚫 Skipping loop body transition 'transition-CombineTextComponent-*************' - should have been executed internally by loop executor
2025-06-28 01:08:24 - TransitionHandler - DEBUG - ✅ Adding exit transition 'transition-MergeDataComponent-*************' to next transitions
2025-06-28 01:08:24 - TransitionHandler - INFO - ✅ Loop completion handled for transition-LoopNode-*************. Next transitions: ['transition-MergeDataComponent-*************']
2025-06-28 01:08:24 - TransitionHandler - INFO - Completed transition transition-LoopNode-************* in 16.46 seconds
2025-06-28 01:08:24 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 16, corr_id ef594c98-6738-4c5c-b073-6c515ed89d02):
2025-06-28 01:08:24 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: ef594c98-6738-4c5c-b073-6c515ed89d02, response: {'result': 'Completed transition in 16.46 seconds', 'message': 'Transition completed in 16.46 seconds', 'transition_id': 'transition-LoopNode-*************', 'status': 'time_logged', 'sequence': 16, 'workflow_status': 'running'}
2025-06-28 01:08:24 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-MergeDataComponent-*************']]
2025-06-28 01:08:24 - EnhancedWorkflowEngine - INFO - Transition transition-LoopNode-************* completed successfully: 1 next transitions
2025-06-28 01:08:24 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-MergeDataComponent-*************']
2025-06-28 01:08:24 - EnhancedWorkflowEngine - INFO - Adding transition transition-MergeDataComponent-************* to pending (all dependencies met)
2025-06-28 01:08:24 - StateManager - DEBUG - Workflow active: {'transition-MergeDataComponent-*************'}
2025-06-28 01:08:24 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:08:24 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:08:24 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:08:24 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:ef594c98-6738-4c5c-b073-6c515ed89d02'
2025-06-28 01:08:24 - RedisManager - DEBUG - Set key 'workflow_state:ef594c98-6738-4c5c-b073-6c515ed89d02' with TTL of 600 seconds
2025-06-28 01:08:24 - StateManager - INFO - Workflow state saved to Redis for workflow ID: ef594c98-6738-4c5c-b073-6c515ed89d02. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:08:24 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 01:08:24 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 01:08:24 - StateManager - INFO - Cleared 1 pending transitions: {'transition-MergeDataComponent-*************'}
2025-06-28 01:08:24 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 01:08:24 - StateManager - INFO - Terminated: False
2025-06-28 01:08:24 - StateManager - INFO - Pending transitions (0): []
2025-06-28 01:08:24 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 01:08:24 - StateManager - INFO - Completed transitions (6): ['current_iteration', 'loop_iteration_0', 'loop_iteration_1', 'loop_iteration_2', 'transition-CombineTextComponent-*************', 'transition-LoopNode-*************']
2025-06-28 01:08:24 - StateManager - INFO - Results stored for 6 transitions
2025-06-28 01:08:24 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 01:08:24 - StateManager - INFO - Workflow status: inactive
2025-06-28 01:08:24 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 01:08:24 - StateManager - INFO - Workflow status: inactive
2025-06-28 01:08:24 - StateManager - INFO - Workflow paused: False
2025-06-28 01:08:24 - StateManager - INFO - ==============================
2025-06-28 01:08:24 - TransitionHandler - INFO - Starting parallel execution of transition: transition-MergeDataComponent-*************
2025-06-28 01:08:24 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 17, corr_id ef594c98-6738-4c5c-b073-6c515ed89d02):
2025-06-28 01:08:24 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: ef594c98-6738-4c5c-b073-6c515ed89d02, response: {'result': 'Starting execution of transition: transition-MergeDataComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'started', 'sequence': 17, 'workflow_status': 'running'}
2025-06-28 01:08:24 - TransitionHandler - EXECUTE - Transition 'transition-MergeDataComponent-*************' (type=standard, execution_type=Components)
2025-06-28 01:08:24 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 01:08:24 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-MergeDataComponent-*************
2025-06-28 01:08:24 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 01:08:24 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 01:08:25 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 01:08:25 - StateManager - DEBUG - Extracted results for 1 tools in transition transition-LoopNode-*************
2025-06-28 01:08:25 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 01:08:25 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 01:08:25 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_results': {'final_results': ['1\ncoming', '2\ncoming', '3\ncoming']}
2025-06-28 01:08:25 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-28 01:08:25 - WorkflowUtils - DEBUG - Path tracking - Found handle 'final_results' directly in source_results
2025-06-28 01:08:25 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 01:08:25 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 01:08:25 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_results': {'final_results': ['1\ncoming', '2\ncoming', '3\ncoming']}
2025-06-28 01:08:25 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-28 01:08:25 - WorkflowUtils - DEBUG - Path tracking - Found handle 'final_results' directly in source_results
2025-06-28 01:08:25 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_results → main_input via path 'final_results': ['1\ncoming', '2\ncoming', '3\ncoming']
2025-06-28 01:08:25 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 01:08:25 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 01:08:25 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-28 01:08:25 - WorkflowUtils - DEBUG - Filtering out field 'output_key_1' with null/empty value: 
2025-06-28 01:08:25 - WorkflowUtils - DEBUG - Filtering out field 'output_key_2' with null/empty value: 
2025-06-28 01:08:25 - WorkflowUtils - DEBUG - Filtering out field 'output_key_3' with null/empty value: 
2025-06-28 01:08:25 - WorkflowUtils - DEBUG - Filtering out field 'output_key_4' with null/empty value: 
2025-06-28 01:08:25 - WorkflowUtils - DEBUG - Filtering out field 'output_key_5' with null/empty value: 
2025-06-28 01:08:25 - WorkflowUtils - DEBUG - Filtering out field 'output_key_6' with null/empty value: 
2025-06-28 01:08:25 - WorkflowUtils - DEBUG - Filtering out field 'output_key_7' with null/empty value: 
2025-06-28 01:08:25 - WorkflowUtils - DEBUG - Filtering out field 'output_key_8' with null/empty value: 
2025-06-28 01:08:25 - WorkflowUtils - DEBUG - Filtering out field 'output_key_9' with null/empty value: 
2025-06-28 01:08:25 - WorkflowUtils - DEBUG - Filtering out field 'output_key_10' with null/empty value: 
2025-06-28 01:08:25 - WorkflowUtils - DEBUG - Filtering out field 'output_key_11' with null/empty value: 
2025-06-28 01:08:25 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with empty collection: {}
2025-06-28 01:08:25 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with empty collection: {}
2025-06-28 01:08:25 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with empty collection: {}
2025-06-28 01:08:25 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with empty collection: {}
2025-06-28 01:08:25 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with empty collection: {}
2025-06-28 01:08:25 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with empty collection: {}
2025-06-28 01:08:25 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with empty collection: {}
2025-06-28 01:08:25 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with empty collection: {}
2025-06-28 01:08:25 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with empty collection: {}
2025-06-28 01:08:25 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with empty collection: {}
2025-06-28 01:08:25 - WorkflowUtils - INFO - 🧹 Parameter filtering: 24 → 2 fields (22 null/empty fields removed)
2025-06-28 01:08:25 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 01:08:25 - TransitionHandler - DEBUG - 📌 Added static parameter: merge_strategy = Aggregate
2025-06-28 01:08:25 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': ['1\ncoming', '2\ncoming', '3\ncoming'], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-06-28 01:08:25 - TransitionHandler - DEBUG - tool Parameters: {'main_input': ['1\ncoming', '2\ncoming', '3\ncoming'], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-06-28 01:08:25 - TransitionHandler - INFO - Invoking tool 'MergeDataComponent' (tool_id: 1) for node 'MergeDataComponent' in transition 'transition-MergeDataComponent-*************' with parameters: {'main_input': ['1\ncoming', '2\ncoming', '3\ncoming'], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-06-28 01:08:25 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 18, corr_id ef594c98-6738-4c5c-b073-6c515ed89d02):
2025-06-28 01:08:25 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: ef594c98-6738-4c5c-b073-6c515ed89d02, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'message': 'Connecting to server', 'result': 'Connecting to server MergeDataComponent', 'status': 'connecting', 'sequence': 18, 'workflow_status': 'running'}
2025-06-28 01:08:25 - NodeExecutor - INFO - Executing tool 'MergeDataComponent' via Kafka (request_id: 3adda4c4-d603-43cc-9387-a29026ec0223) using provided producer.
2025-06-28 01:08:25 - NodeExecutor - DEBUG - Added correlation_id ef594c98-6738-4c5c-b073-6c515ed89d02 to payload
2025-06-28 01:08:25 - NodeExecutor - DEBUG - Added transition_id transition-MergeDataComponent-************* to payload
2025-06-28 01:08:25 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'MergeDataComponent', 'tool_parameters': {'main_input': ['1\ncoming', '2\ncoming', '3\ncoming'], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}, 'request_id': '3adda4c4-d603-43cc-9387-a29026ec0223', 'correlation_id': 'ef594c98-6738-4c5c-b073-6c515ed89d02', 'transition_id': 'transition-MergeDataComponent-*************'}
2025-06-28 01:08:25 - NodeExecutor - DEBUG - Request 3adda4c4-d603-43cc-9387-a29026ec0223 sent successfully using provided producer.
2025-06-28 01:08:25 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 3adda4c4-d603-43cc-9387-a29026ec0223...
2025-06-28 01:08:27 - NodeExecutor - DEBUG - Result consumer received message: Offset=811
2025-06-28 01:08:27 - NodeExecutor - DEBUG - Received valid result for request_id 3adda4c4-d603-43cc-9387-a29026ec0223
2025-06-28 01:08:27 - NodeExecutor - INFO - Result received for request 3adda4c4-d603-43cc-9387-a29026ec0223.
2025-06-28 01:08:27 - TransitionHandler - INFO - Execution result from Components executor: [
  "1\ncoming",
  "2\ncoming",
  "3\ncoming"
]
2025-06-28 01:08:27 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 19, corr_id ef594c98-6738-4c5c-b073-6c515ed89d02):
2025-06-28 01:08:27 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: ef594c98-6738-4c5c-b073-6c515ed89d02, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'message': 'Transition Result received.', 'result': ['1\ncoming', '2\ncoming', '3\ncoming'], 'status': 'completed', 'sequence': 19, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 01:08:27 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in memory: {'MergeDataComponent': {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'result': {'result': ['1\ncoming', '2\ncoming', '3\ncoming']}, 'status': 'completed', 'timestamp': 1751053107.167296}}
2025-06-28 01:08:27 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-MergeDataComponent-*************'
2025-06-28 01:08:27 - RedisManager - DEBUG - Set key 'result:transition-MergeDataComponent-*************' with TTL of 300 seconds
2025-06-28 01:08:27 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:08:27 - StateManager - INFO - Marked transition transition-MergeDataComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 01:08:27 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-MergeDataComponent-*************', 'transition-CombineTextComponent-*************', 'loop_iteration_2', 'loop_iteration_1', 'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_0'}
2025-06-28 01:08:27 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-MergeDataComponent-*************
2025-06-28 01:08:27 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'list'>
2025-06-28 01:08:27 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 01:08:27 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'list'>
2025-06-28 01:08:27 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-MergeDataComponent-*************:
2025-06-28 01:08:27 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 01:08:27 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 01:08:27 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-MergeDataComponent-*************, returning empty list
2025-06-28 01:08:27 - TransitionHandler - INFO - Completed transition transition-MergeDataComponent-************* in 3.11 seconds
2025-06-28 01:08:27 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 20, corr_id ef594c98-6738-4c5c-b073-6c515ed89d02):
2025-06-28 01:08:27 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: ef594c98-6738-4c5c-b073-6c515ed89d02, response: {'result': 'Completed transition in 3.11 seconds', 'message': 'Transition completed in 3.11 seconds', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'time_logged', 'sequence': 20, 'workflow_status': 'running'}
2025-06-28 01:08:27 - EnhancedWorkflowEngine - DEBUG - Results: [[]]
2025-06-28 01:08:27 - StateManager - INFO - Workflow terminated flag set to: True
2025-06-28 01:08:27 - EnhancedWorkflowEngine - INFO - Workflow is marked as terminated, ending workflow execution.
2025-06-28 01:08:27 - StateManager - DEBUG - Workflow active: False (terminated=True, pending=empty, waiting=empty)
2025-06-28 01:08:27 - EnhancedWorkflowEngine - INFO - Workflow execution completed.
2025-06-28 01:08:27 - KafkaWorkflowConsumer - INFO - Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' final status: completed, result: Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' executed successfully.
2025-06-28 01:08:27 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: ef594c98-6738-4c5c-b073-6c515ed89d02, response: {'status': 'complete', 'result': "Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' executed successfully.", 'workflow_status': 'completed'}
2025-06-28 01:08:27 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: ef594c98-6738-4c5c-b073-6c515ed89d02 
2025-06-28 01:09:23 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:09:24 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:09:24 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:09:24 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:10:23 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:10:24 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:10:24 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:10:24 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:11:23 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:11:24 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:11:24 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:11:24 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:12:23 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:12:24 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:12:24 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:12:24 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:12:40 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=994
2025-06-28 01:12:40 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1751053360, 'task_type': 'workflow', 'data': {'workflow_id': 'feda07bf-a91e-4004-80cb-72416cdb5a43', 'payload': {'user_dependent_fields': ['main_input'], 'user_payload_template': {'main_input': {'value': ['a', 'b', 'c'], 'transition_id': 'MergeDataComponent-*************'}}}, 'approval': True, 'user_id': 'c1454e90-09ac-40f2-bde2-833387d7b645'}, 'approval': True}
2025-06-28 01:12:40 - KafkaWorkflowConsumer - INFO - Extracted user_id: c1454e90-09ac-40f2-bde2-833387d7b645 for workflow: feda07bf-a91e-4004-80cb-72416cdb5a43
2025-06-28 01:12:40 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/feda07bf-a91e-4004-80cb-72416cdb5a43
2025-06-28 01:12:40 - WorkflowService - DEBUG - Received response with status code: 200
2025-06-28 01:12:40 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow each loop testing retrieved successfully",
  "workflow": {
    "id": "feda07bf-a91e-4004-80cb-72416cdb5a43",
    "name": "each loop testing",
    "description": "each_loop_testing",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/8a9cb2ef-a4fb-4644-a09a-4f80a8c1e068.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/b833e8b5-f462-4115-a485-82fa6f1f4e89.json",
    "start_nodes": [
      {
        "field": "main_input",
        "type": "dict",
        "transition_id": "transition-MergeDataComponent-*************"
      }
    ],
    "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645",
    "user_ids": [
      "c1454e90-09ac-40f2-bde2-833387d7b645"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-24T11:13:46.129953",
    "updated_at": "2025-06-27T19:40:56.298102",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      },
      {
        "name": "LoopNode",
        "display_name": "For Each Loop",
        "type": "component",
        "transition_id": "transition-LoopNode-*************"
      },
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-*************"
      },
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-*************"
      }
    ],
    "is_updated": true
  }
}
2025-06-28 01:12:41 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for feda07bf-a91e-4004-80cb-72416cdb5a43 - server_script_path is optional
2025-06-28 01:12:41 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-06-28 01:12:41 - StateManager - DEBUG - Using global database connections from initializer
2025-06-28 01:12:41 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 01:12:41 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 01:12:41 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 01:12:42 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 01:12:42 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 01:12:42 - KafkaWorkflowConsumer - ERROR - Engine initialization error: {'iteration_list': None, 'batch_size': 1} is not valid under any of the given schemas

Failed validating 'oneOf' in schema['properties']['transitions']['items']['properties']['loop_config']['properties']['iteration_source']:
    {'oneOf': [{'type': 'object',
                'description': 'Iteration using a static list of items',
                'properties': {'iteration_list': {'type': 'array',
                                                  'description': 'List of '
                                                                 'items to '
                                                                 'iterate '
                                                                 'over',
                                                  'items': {'oneOf': [{'type': 'string'},
                                                                      {'type': 'number'},
                                                                      {'type': 'boolean'},
                                                                      {'type': 'object'}]}},
                               'batch_size': {'type': 'integer',
                                              'minimum': 1,
                                              'description': 'Optional: '
                                                             'Process '
                                                             'items in '
                                                             'batches of '
                                                             'this size',
                                              'default': 1}},
                'required': ['iteration_list']},
               {'type': 'object',
                'description': 'Iteration using a number range',
                'properties': {'number_range': {'type': 'object',
                                                'properties': {'start': {'type': 'integer',
                                                                         'description': 'Starting '
                                                                                        'number'},
                                                               'end': {'type': 'integer',
                                                                       'description': 'Ending '
                                                                                      'number '
                                                                                      '(inclusive)'}},
                                                'required': ['start',
                                                             'end']},
                               'step': {'type': 'integer',
                                        'description': 'Step size',
                                        'default': 1,
                                        'minimum': 1}},
                'required': ['number_range']}]}

On instance['transitions'][1]['loop_config']['iteration_source']:
    {'iteration_list': None, 'batch_size': 1}
2025-06-28 01:12:42 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 15a52c5f-f550-4236-80c0-f1f4fd7b46e9, response: {'status': 'error', 'workflow_status': 'failed', 'result': "Engine initialization error: {'iteration_list': None, 'batch_size': 1} is not valid under any of the given schemas\n\nFailed validating 'oneOf' in schema['properties']['transitions']['items']['properties']['loop_config']['properties']['iteration_source']:\n    {'oneOf': [{'type': 'object',\n                'description': 'Iteration using a static list of items',\n                'properties': {'iteration_list': {'type': 'array',\n                                                  'description': 'List of '\n                                                                 'items to '\n                                                                 'iterate '\n                                                                 'over',\n                                                  'items': {'oneOf': [{'type': 'string'},\n                                                                      {'type': 'number'},\n                                                                      {'type': 'boolean'},\n                                                                      {'type': 'object'}]}},\n                               'batch_size': {'type': 'integer',\n                                              'minimum': 1,\n                                              'description': 'Optional: '\n                                                             'Process '\n                                                             'items in '\n                                                             'batches of '\n                                                             'this size',\n                                              'default': 1}},\n                'required': ['iteration_list']},\n               {'type': 'object',\n                'description': 'Iteration using a number range',\n                'properties': {'number_range': {'type': 'object',\n                                                'properties': {'start': {'type': 'integer',\n                                                                         'description': 'Starting '\n                                                                                        'number'},\n                                                               'end': {'type': 'integer',\n                                                                       'description': 'Ending '\n                                                                                      'number '\n                                                                                      '(inclusive)'}},\n                                                'required': ['start',\n                                                             'end']},\n                               'step': {'type': 'integer',\n                                        'description': 'Step size',\n                                        'default': 1,\n                                        'minimum': 1}},\n                'required': ['number_range']}]}\n\nOn instance['transitions'][1]['loop_config']['iteration_source']:\n    {'iteration_list': None, 'batch_size': 1}", 'message': "Engine initialization error: {'iteration_list': None, 'batch_size': 1} is not valid under any of the given schemas\n\nFailed validating 'oneOf' in schema['properties']['transitions']['items']['properties']['loop_config']['properties']['iteration_source']:\n    {'oneOf': [{'type': 'object',\n                'description': 'Iteration using a static list of items',\n                'properties': {'iteration_list': {'type': 'array',\n                                                  'description': 'List of '\n                                                                 'items to '\n                                                                 'iterate '\n                                                                 'over',\n                                                  'items': {'oneOf': [{'type': 'string'},\n                                                                      {'type': 'number'},\n                                                                      {'type': 'boolean'},\n                                                                      {'type': 'object'}]}},\n                               'batch_size': {'type': 'integer',\n                                              'minimum': 1,\n                                              'description': 'Optional: '\n                                                             'Process '\n                                                             'items in '\n                                                             'batches of '\n                                                             'this size',\n                                              'default': 1}},\n                'required': ['iteration_list']},\n               {'type': 'object',\n                'description': 'Iteration using a number range',\n                'properties': {'number_range': {'type': 'object',\n                                                'properties': {'start': {'type': 'integer',\n                                                                         'description': 'Starting '\n                                                                                        'number'},\n                                                               'end': {'type': 'integer',\n                                                                       'description': 'Ending '\n                                                                                      'number '\n                                                                                      '(inclusive)'}},\n                                                'required': ['start',\n                                                             'end']},\n                               'step': {'type': 'integer',\n                                        'description': 'Step size',\n                                        'default': 1,\n                                        'minimum': 1}},\n                'required': ['number_range']}]}\n\nOn instance['transitions'][1]['loop_config']['iteration_source']:\n    {'iteration_list': None, 'batch_size': 1}", 'sequence': 0}
2025-06-28 01:12:55 - RedisEventListener - DEBUG - Raw state DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@6__:*', 'channel': b'__keyspace@6__:workflow_state:4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152', 'data': b'expired'}
2025-06-28 01:12:55 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@6__:workflow_state:4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152'
2025-06-28 01:12:55 - RedisEventListener - DEBUG - Decoded channel: __keyspace@6__:workflow_state:4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152
2025-06-28 01:12:55 - RedisEventListener - DEBUG - Extracted key: workflow_state:4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152
2025-06-28 01:12:55 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-28 01:12:55 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-28 01:12:55 - RedisEventListener - INFO - Detected expired event for workflow state of workflow: 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152
2025-06-28 01:12:55 - RedisEventListener - INFO - Archiving workflow state for workflow: 4e0533fa-a9b6-4dc5-9f6b-dd99d7e5f152
2025-06-28 01:12:59 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 01:13:00 - PostgresManager - DEBUG - Inserted new workflow state for correlation_id: 15a52c5f-f550-4236-80c0-f1f4fd7b46e9
2025-06-28 01:13:00 - StateManager - INFO - Archived workflow state to PostgreSQL for workflow ID: 15a52c5f-f550-4236-80c0-f1f4fd7b46e9
2025-06-28 01:13:23 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-CombineTextComponent-*************', 'data': b'expired'}
2025-06-28 01:13:23 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-CombineTextComponent-*************'
2025-06-28 01:13:23 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-CombineTextComponent-*************
2025-06-28 01:13:23 - RedisEventListener - DEBUG - Extracted key: result:transition-CombineTextComponent-*************
2025-06-28 01:13:23 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-28 01:13:23 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-28 01:13:23 - RedisEventListener - INFO - Detected expired event for result of transition: transition-CombineTextComponent-*************
2025-06-28 01:13:23 - RedisEventListener - INFO - Archiving result for transition: transition-CombineTextComponent-*************
2025-06-28 01:13:23 - StateManager - DEBUG - Attempting to archive result for transition transition-CombineTextComponent-*************
2025-06-28 01:13:24 - StateManager - DEBUG - Provided result: False
2025-06-28 01:13:24 - StateManager - DEBUG - Trying to get result from Redis for transition transition-CombineTextComponent-*************
2025-06-28 01:13:25 - StateManager - DEBUG - No result found in Redis for transition transition-CombineTextComponent-*************
2025-06-28 01:13:25 - StateManager - DEBUG - Trying to get result from memory for transition transition-CombineTextComponent-*************
2025-06-28 01:13:25 - StateManager - DEBUG - No result found in memory for transition transition-CombineTextComponent-*************
2025-06-28 01:13:25 - StateManager - DEBUG - Available transition results in memory: []
2025-06-28 01:13:25 - StateManager - WARNING - No result found to archive for transition transition-CombineTextComponent-*************
2025-06-28 01:13:25 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:13:25 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pmessage', b'__keyspace@5__:*', b'__keyspace@5__:result:transition-LoopNode-*************', b'expired']
2025-06-28 01:13:25 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:13:25 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:13:27 - KafkaWorkflowConsumer - INFO - Consumer task cancelled.
2025-06-28 01:13:27 - NodeExecutor - INFO - Result consumer loop cancelled.
2025-06-28 01:13:27 - MCPToolExecutor - INFO - Result consumer loop cancelled.
2025-06-28 01:13:27 - AgentExecutor - INFO - Result consumer loop cancelled.
2025-06-28 01:13:27 - Main - ERROR - Shutting down due to keyboard interrupt...
2025-06-28 01:20:53 - Main - INFO - Starting Server
2025-06-28 01:20:53 - Main - INFO - Connection at: **************:9092
2025-06-28 01:20:53 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-28 01:20:53 - NodeExecutor - INFO - NodeExecutor initialized.
2025-06-28 01:20:53 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-28 01:20:53 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-28 01:20:53 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 01:20:55 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 01:20:55 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-28 01:20:57 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-28 01:20:59 - PostgresManager - INFO - PostgreSQL connection pool created
2025-06-28 01:20:59 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-06-28 01:21:01 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 01:21:02 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-06-28 01:21:02 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 01:21:04 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 01:21:04 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-28 01:21:06 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-28 01:21:06 - RedisEventListener - INFO - Starting Redis event listener thread
2025-06-28 01:21:06 - RedisEventListener - INFO - Redis event listener started
2025-06-28 01:21:06 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-06-28 01:21:06 - StateManager - DEBUG - Using provided database connections
2025-06-28 01:21:06 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 01:21:06 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 01:21:06 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 01:21:06 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-06-28 01:21:06 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 01:21:06 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 01:21:06 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-06-28 01:21:06 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-06-28 01:21:07 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-06-28 01:21:07 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-06-28 01:21:09 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-06-28 01:21:09 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-06-28 01:21:09 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-06-28 01:21:14 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-06-28 01:21:20 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-06-28 01:21:20 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-06-28 01:21:20 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-06-28 01:21:27 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-06-28 01:21:27 - NodeExecutor - INFO - Background result consumer loop started.
2025-06-28 01:21:27 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-06-28 01:21:33 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-06-28 01:21:33 - AgentExecutor - INFO - Background result consumer loop started.
2025-06-28 01:22:02 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:22:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:22:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:22:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:23:02 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:23:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:23:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:23:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:23:06 - KafkaWorkflowConsumer - INFO - Consumer task cancelled.
2025-06-28 01:23:07 - NodeExecutor - INFO - Result consumer loop cancelled.
2025-06-28 01:23:07 - AgentExecutor - INFO - Result consumer loop cancelled.
2025-06-28 01:23:07 - MCPToolExecutor - INFO - Result consumer loop cancelled.
2025-06-28 01:23:07 - Main - ERROR - Shutting down due to keyboard interrupt...
2025-06-28 01:24:06 - Main - INFO - Starting Server
2025-06-28 01:24:06 - Main - INFO - Connection at: **************:9092
2025-06-28 01:24:06 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-28 01:24:06 - NodeExecutor - INFO - NodeExecutor initialized.
2025-06-28 01:24:06 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-28 01:24:06 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-28 01:24:06 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 01:24:08 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 01:24:08 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-28 01:24:09 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-28 01:24:11 - PostgresManager - INFO - PostgreSQL connection pool created
2025-06-28 01:24:11 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-06-28 01:24:14 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 01:24:15 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-06-28 01:24:15 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 01:24:16 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 01:24:16 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-28 01:24:18 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-28 01:24:18 - RedisEventListener - INFO - Starting Redis event listener thread
2025-06-28 01:24:18 - RedisEventListener - INFO - Redis event listener started
2025-06-28 01:24:18 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-06-28 01:24:18 - StateManager - DEBUG - Using provided database connections
2025-06-28 01:24:18 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 01:24:18 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 01:24:18 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 01:24:19 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-06-28 01:24:19 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 01:24:19 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 01:24:19 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-06-28 01:24:19 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-06-28 01:24:19 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-06-28 01:24:19 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-06-28 01:24:22 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-06-28 01:24:22 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-06-28 01:24:22 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-06-28 01:24:26 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-06-28 01:24:33 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-06-28 01:24:33 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-06-28 01:24:33 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-06-28 01:24:39 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-06-28 01:24:39 - NodeExecutor - INFO - Background result consumer loop started.
2025-06-28 01:24:39 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-06-28 01:24:45 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-06-28 01:24:45 - AgentExecutor - INFO - Background result consumer loop started.
2025-06-28 01:25:15 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:25:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:25:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:25:15 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:26:15 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:26:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:26:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:26:15 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:27:15 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:27:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:27:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:27:15 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:28:15 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:28:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:28:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:28:15 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:29:15 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:29:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:29:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:29:15 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:30:15 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:30:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:30:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:30:15 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:31:15 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:31:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:31:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:31:15 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:32:15 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:32:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:32:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:32:15 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:33:15 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:33:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:33:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:33:15 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:34:15 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:34:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:34:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:34:15 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:35:15 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:35:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:35:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:35:15 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:36:15 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:36:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:36:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:36:15 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:37:02 - KafkaWorkflowConsumer - INFO - Consumer task cancelled.
2025-06-28 01:37:02 - NodeExecutor - INFO - Result consumer loop cancelled.
2025-06-28 01:37:02 - MCPToolExecutor - INFO - Result consumer loop cancelled.
2025-06-28 01:37:02 - AgentExecutor - INFO - Result consumer loop cancelled.
2025-06-28 01:37:02 - Main - ERROR - Shutting down due to keyboard interrupt...
2025-06-28 01:37:04 - Main - INFO - Starting Server
2025-06-28 01:37:04 - Main - INFO - Connection at: **************:9092
2025-06-28 01:37:04 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-28 01:37:04 - NodeExecutor - INFO - NodeExecutor initialized.
2025-06-28 01:37:04 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-28 01:37:04 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-28 01:37:04 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 01:37:06 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 01:37:06 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-28 01:37:08 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-28 01:37:10 - PostgresManager - INFO - PostgreSQL connection pool created
2025-06-28 01:37:10 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-06-28 01:37:12 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 01:37:13 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-06-28 01:37:13 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 01:37:15 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 01:37:15 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-28 01:37:16 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-28 01:37:16 - RedisEventListener - INFO - Starting Redis event listener thread
2025-06-28 01:37:16 - RedisEventListener - INFO - Redis event listener started
2025-06-28 01:37:16 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-06-28 01:37:16 - StateManager - DEBUG - Using provided database connections
2025-06-28 01:37:16 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 01:37:16 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 01:37:16 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 01:37:17 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-06-28 01:37:17 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 01:37:17 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 01:37:17 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-06-28 01:37:17 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-06-28 01:37:17 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-06-28 01:37:17 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-06-28 01:37:20 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-06-28 01:37:20 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-06-28 01:37:20 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-06-28 01:37:33 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-06-28 01:37:40 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-06-28 01:37:40 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-06-28 01:37:40 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-06-28 01:37:46 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-06-28 01:37:46 - NodeExecutor - INFO - Background result consumer loop started.
2025-06-28 01:37:46 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-06-28 01:37:53 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-06-28 01:37:53 - AgentExecutor - INFO - Background result consumer loop started.
2025-06-28 01:37:59 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=995
2025-06-28 01:37:59 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1751054879, 'task_type': 'workflow', 'data': {'workflow_id': 'feda07bf-a91e-4004-80cb-72416cdb5a43', 'payload': {'user_dependent_fields': ['main_input'], 'user_payload_template': {'main_input': {'value': ['a', 'b', 'c'], 'transition_id': 'MergeDataComponent-*************'}}}, 'approval': True, 'user_id': 'c1454e90-09ac-40f2-bde2-833387d7b645'}, 'approval': True}
2025-06-28 01:37:59 - KafkaWorkflowConsumer - INFO - Extracted user_id: c1454e90-09ac-40f2-bde2-833387d7b645 for workflow: feda07bf-a91e-4004-80cb-72416cdb5a43
2025-06-28 01:37:59 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/feda07bf-a91e-4004-80cb-72416cdb5a43
2025-06-28 01:38:00 - WorkflowService - DEBUG - Received response with status code: 200
2025-06-28 01:38:00 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow each loop testing retrieved successfully",
  "workflow": {
    "id": "feda07bf-a91e-4004-80cb-72416cdb5a43",
    "name": "each loop testing",
    "description": "each_loop_testing",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/8a9cb2ef-a4fb-4644-a09a-4f80a8c1e068.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/b833e8b5-f462-4115-a485-82fa6f1f4e89.json",
    "start_nodes": [
      {
        "field": "main_input",
        "type": "dict",
        "transition_id": "transition-MergeDataComponent-*************"
      }
    ],
    "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645",
    "user_ids": [
      "c1454e90-09ac-40f2-bde2-833387d7b645"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-24T11:13:46.129953",
    "updated_at": "2025-06-27T19:40:56.298102",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      },
      {
        "name": "LoopNode",
        "display_name": "For Each Loop",
        "type": "component",
        "transition_id": "transition-LoopNode-*************"
      },
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-*************"
      },
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-*************"
      }
    ],
    "is_updated": true
  }
}
2025-06-28 01:38:00 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for feda07bf-a91e-4004-80cb-72416cdb5a43 - server_script_path is optional
2025-06-28 01:38:00 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-06-28 01:38:00 - StateManager - DEBUG - Using global database connections from initializer
2025-06-28 01:38:00 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 01:38:00 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 01:38:00 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 01:38:01 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 01:38:01 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 01:38:01 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-06-28 01:38:01 - StateManager - DEBUG - Using provided database connections
2025-06-28 01:38:01 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 01:38:01 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 01:38:01 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 01:38:02 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 01:38:02 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 01:38:02 - StateManager - DEBUG - Extracted dependencies for transition transition-LoopNode-*************: ['transition-MergeDataComponent-*************']
2025-06-28 01:38:02 - StateManager - DEBUG - Extracted dependencies for transition transition-MergeDataComponent-*************: ['transition-LoopNode-*************']
2025-06-28 01:38:02 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-LoopNode-*************']
2025-06-28 01:38:02 - StateManager - INFO - Built dependency map for 4 transitions
2025-06-28 01:38:02 - StateManager - DEBUG - Transition transition-LoopNode-************* depends on: ['transition-MergeDataComponent-*************']
2025-06-28 01:38:02 - StateManager - DEBUG - Transition transition-MergeDataComponent-************* depends on: ['transition-LoopNode-*************']
2025-06-28 01:38:02 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* depends on: ['transition-LoopNode-*************']
2025-06-28 01:38:02 - MCPToolExecutor - DEBUG - Set correlation ID to: 867d9536-c215-4e26-a4f4-2713d7076ca7
2025-06-28 01:38:02 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 867d9536-c215-4e26-a4f4-2713d7076ca7 in tool_executor
2025-06-28 01:38:02 - MCPToolExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-06-28 01:38:02 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in tool_executor
2025-06-28 01:38:02 - NodeExecutor - DEBUG - Set correlation ID to: 867d9536-c215-4e26-a4f4-2713d7076ca7
2025-06-28 01:38:02 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 867d9536-c215-4e26-a4f4-2713d7076ca7 in node_executor
2025-06-28 01:38:02 - AgentExecutor - DEBUG - Set correlation ID to: 867d9536-c215-4e26-a4f4-2713d7076ca7
2025-06-28 01:38:02 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 867d9536-c215-4e26-a4f4-2713d7076ca7 in agent_executor
2025-06-28 01:38:02 - AgentExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-06-28 01:38:02 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in agent_executor
2025-06-28 01:38:02 - TransitionHandler - INFO - TransitionHandler initialized
2025-06-28 01:38:02 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: 867d9536-c215-4e26-a4f4-2713d7076ca7
2025-06-28 01:38:02 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: 867d9536-c215-4e26-a4f4-2713d7076ca7
2025-06-28 01:38:02 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 867d9536-c215-4e26-a4f4-2713d7076ca7, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-06-28 01:38:02 - StateManager - INFO - Workflow initialized with initial transition: transition-MergeDataComponent-*************
2025-06-28 01:38:02 - StateManager - DEBUG - State: pending={'transition-MergeDataComponent-*************'}, waiting=set(), completed=set()
2025-06-28 01:38:02 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-MergeDataComponent-*************
2025-06-28 01:38:02 - StateManager - DEBUG - Workflow active: {'transition-MergeDataComponent-*************'}
2025-06-28 01:38:03 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:867d9536-c215-4e26-a4f4-2713d7076ca7'
2025-06-28 01:38:03 - RedisManager - DEBUG - Set key 'workflow_state:867d9536-c215-4e26-a4f4-2713d7076ca7' with TTL of 600 seconds
2025-06-28 01:38:03 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 867d9536-c215-4e26-a4f4-2713d7076ca7. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:38:03 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 01:38:03 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 01:38:03 - StateManager - INFO - Cleared 1 pending transitions: {'transition-MergeDataComponent-*************'}
2025-06-28 01:38:03 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 01:38:03 - StateManager - INFO - Terminated: False
2025-06-28 01:38:03 - StateManager - INFO - Pending transitions (0): []
2025-06-28 01:38:03 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 01:38:03 - StateManager - INFO - Completed transitions (0): []
2025-06-28 01:38:03 - StateManager - INFO - Results stored for 0 transitions
2025-06-28 01:38:03 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 01:38:03 - StateManager - INFO - Workflow status: inactive
2025-06-28 01:38:03 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 01:38:03 - StateManager - INFO - Workflow status: inactive
2025-06-28 01:38:03 - StateManager - INFO - Workflow paused: False
2025-06-28 01:38:03 - StateManager - INFO - ==============================
2025-06-28 01:38:03 - TransitionHandler - INFO - Starting parallel execution of transition: transition-MergeDataComponent-*************
2025-06-28 01:38:03 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id 867d9536-c215-4e26-a4f4-2713d7076ca7):
2025-06-28 01:38:03 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 867d9536-c215-4e26-a4f4-2713d7076ca7, response: {'result': 'Starting execution of transition: transition-MergeDataComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-06-28 01:38:03 - TransitionHandler - EXECUTE - Transition 'transition-MergeDataComponent-*************' (type=initial, execution_type=Components)
2025-06-28 01:38:03 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 01:38:03 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-MergeDataComponent-*************
2025-06-28 01:38:03 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 01:38:03 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 01:38:03 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-28 01:38:03 - WorkflowUtils - DEBUG - Filtering out field 'output_key_1' with null/empty value: 
2025-06-28 01:38:03 - WorkflowUtils - DEBUG - Filtering out field 'output_key_2' with null/empty value: 
2025-06-28 01:38:03 - WorkflowUtils - DEBUG - Filtering out field 'output_key_3' with null/empty value: 
2025-06-28 01:38:03 - WorkflowUtils - DEBUG - Filtering out field 'output_key_4' with null/empty value: 
2025-06-28 01:38:03 - WorkflowUtils - DEBUG - Filtering out field 'output_key_5' with null/empty value: 
2025-06-28 01:38:03 - WorkflowUtils - DEBUG - Filtering out field 'output_key_6' with null/empty value: 
2025-06-28 01:38:03 - WorkflowUtils - DEBUG - Filtering out field 'output_key_7' with null/empty value: 
2025-06-28 01:38:03 - WorkflowUtils - DEBUG - Filtering out field 'output_key_8' with null/empty value: 
2025-06-28 01:38:03 - WorkflowUtils - DEBUG - Filtering out field 'output_key_9' with null/empty value: 
2025-06-28 01:38:03 - WorkflowUtils - DEBUG - Filtering out field 'output_key_10' with null/empty value: 
2025-06-28 01:38:03 - WorkflowUtils - DEBUG - Filtering out field 'output_key_11' with null/empty value: 
2025-06-28 01:38:03 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with empty collection: {}
2025-06-28 01:38:03 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with empty collection: {}
2025-06-28 01:38:03 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with empty collection: {}
2025-06-28 01:38:03 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with empty collection: {}
2025-06-28 01:38:03 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with empty collection: {}
2025-06-28 01:38:03 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with empty collection: {}
2025-06-28 01:38:03 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with empty collection: {}
2025-06-28 01:38:03 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with empty collection: {}
2025-06-28 01:38:03 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with empty collection: {}
2025-06-28 01:38:03 - WorkflowUtils - INFO - 🧹 Parameter filtering: 24 → 4 fields (20 null/empty fields removed)
2025-06-28 01:38:03 - TransitionHandler - DEBUG - tool Parameters: {'main_input': ['a', 'b', 'c'], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate', 'input_1': ['hello']}
2025-06-28 01:38:03 - TransitionHandler - INFO - Invoking tool 'MergeDataComponent' (tool_id: 1) for node 'MergeDataComponent' in transition 'transition-MergeDataComponent-*************' with parameters: {'main_input': ['a', 'b', 'c'], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate', 'input_1': ['hello']}
2025-06-28 01:38:03 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id 867d9536-c215-4e26-a4f4-2713d7076ca7):
2025-06-28 01:38:03 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 867d9536-c215-4e26-a4f4-2713d7076ca7, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'message': 'Connecting to server', 'result': 'Connecting to server MergeDataComponent', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-28 01:38:03 - NodeExecutor - INFO - Executing tool 'MergeDataComponent' via Kafka (request_id: 1a5eb005-e66d-4f32-9c7b-ef73db357f5b) using provided producer.
2025-06-28 01:38:03 - NodeExecutor - DEBUG - Added correlation_id 867d9536-c215-4e26-a4f4-2713d7076ca7 to payload
2025-06-28 01:38:03 - NodeExecutor - DEBUG - Added transition_id transition-MergeDataComponent-************* to payload
2025-06-28 01:38:03 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'MergeDataComponent', 'tool_parameters': {'main_input': ['a', 'b', 'c'], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate', 'input_1': ['hello']}, 'request_id': '1a5eb005-e66d-4f32-9c7b-ef73db357f5b', 'correlation_id': '867d9536-c215-4e26-a4f4-2713d7076ca7', 'transition_id': 'transition-MergeDataComponent-*************'}
2025-06-28 01:38:03 - NodeExecutor - DEBUG - Request 1a5eb005-e66d-4f32-9c7b-ef73db357f5b sent successfully using provided producer.
2025-06-28 01:38:03 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 1a5eb005-e66d-4f32-9c7b-ef73db357f5b...
2025-06-28 01:38:04 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 995, corr_id: 867d9536-c215-4e26-a4f4-2713d7076ca7
2025-06-28 01:38:05 - NodeExecutor - DEBUG - Result consumer received message: Offset=812
2025-06-28 01:38:05 - NodeExecutor - DEBUG - Received valid result for request_id 1a5eb005-e66d-4f32-9c7b-ef73db357f5b
2025-06-28 01:38:05 - NodeExecutor - INFO - Result received for request 1a5eb005-e66d-4f32-9c7b-ef73db357f5b.
2025-06-28 01:38:05 - TransitionHandler - INFO - Execution result from Components executor: [
  "a",
  "b",
  "c",
  "hello"
]
2025-06-28 01:38:05 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id 867d9536-c215-4e26-a4f4-2713d7076ca7):
2025-06-28 01:38:05 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 867d9536-c215-4e26-a4f4-2713d7076ca7, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'message': 'Transition Result received.', 'result': ['a', 'b', 'c', 'hello'], 'status': 'completed', 'sequence': 2, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 01:38:05 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in memory: {'MergeDataComponent': {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'result': {'result': ['a', 'b', 'c', 'hello']}, 'status': 'completed', 'timestamp': 1751054885.134186}}
2025-06-28 01:38:06 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-MergeDataComponent-*************'
2025-06-28 01:38:06 - RedisManager - DEBUG - Set key 'result:transition-MergeDataComponent-*************' with TTL of 300 seconds
2025-06-28 01:38:06 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:38:06 - StateManager - INFO - Marked transition transition-MergeDataComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 01:38:06 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-MergeDataComponent-*************'}
2025-06-28 01:38:06 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-MergeDataComponent-*************
2025-06-28 01:38:06 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'list'>
2025-06-28 01:38:06 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 01:38:06 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'list'>
2025-06-28 01:38:06 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-MergeDataComponent-*************:
2025-06-28 01:38:06 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 01:38:06 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 01:38:06 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-MergeDataComponent-*************, returning empty list
2025-06-28 01:38:06 - TransitionHandler - INFO - Completed transition transition-MergeDataComponent-************* in 2.66 seconds
2025-06-28 01:38:06 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id 867d9536-c215-4e26-a4f4-2713d7076ca7):
2025-06-28 01:38:06 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 867d9536-c215-4e26-a4f4-2713d7076ca7, response: {'result': 'Completed transition in 2.66 seconds', 'message': 'Transition completed in 2.66 seconds', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'time_logged', 'sequence': 3, 'workflow_status': 'running'}
2025-06-28 01:38:06 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-LoopNode-*************']]
2025-06-28 01:38:06 - EnhancedWorkflowEngine - INFO - Transition transition-MergeDataComponent-************* completed successfully: 1 next transitions
2025-06-28 01:38:06 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-LoopNode-*************']
2025-06-28 01:38:06 - EnhancedWorkflowEngine - INFO - Adding transition transition-LoopNode-************* to pending (all dependencies met)
2025-06-28 01:38:06 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-28 01:38:06 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:867d9536-c215-4e26-a4f4-2713d7076ca7'
2025-06-28 01:38:07 - RedisManager - DEBUG - Set key 'workflow_state:867d9536-c215-4e26-a4f4-2713d7076ca7' with TTL of 600 seconds
2025-06-28 01:38:07 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 867d9536-c215-4e26-a4f4-2713d7076ca7. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:38:07 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 01:38:07 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 01:38:07 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-28 01:38:07 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 01:38:07 - StateManager - INFO - Terminated: False
2025-06-28 01:38:07 - StateManager - INFO - Pending transitions (0): []
2025-06-28 01:38:07 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 01:38:07 - StateManager - INFO - Completed transitions (1): ['transition-MergeDataComponent-*************']
2025-06-28 01:38:07 - StateManager - INFO - Results stored for 1 transitions
2025-06-28 01:38:07 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 01:38:07 - StateManager - INFO - Workflow status: inactive
2025-06-28 01:38:07 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 01:38:07 - StateManager - INFO - Workflow status: inactive
2025-06-28 01:38:07 - StateManager - INFO - Workflow paused: False
2025-06-28 01:38:07 - StateManager - INFO - ==============================
2025-06-28 01:38:07 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-28 01:38:07 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id 867d9536-c215-4e26-a4f4-2713d7076ca7):
2025-06-28 01:38:07 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 867d9536-c215-4e26-a4f4-2713d7076ca7, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 4, 'workflow_status': 'running'}
2025-06-28 01:38:07 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=standard, execution_type=loop)
2025-06-28 01:38:07 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-28 01:38:07 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-28 01:38:07 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-28 01:38:07 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-28 01:38:07 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-28 01:38:08 - StateManager - DEBUG - Retrieved result for transition transition-MergeDataComponent-************* from Redis
2025-06-28 01:38:08 - StateManager - DEBUG - Detected wrapped result structure for transition transition-MergeDataComponent-*************, extracting data
2025-06-28 01:38:08 - StateManager - DEBUG - Extracted double-nested result data for transition transition-MergeDataComponent-*************
2025-06-28 01:38:08 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-MergeDataComponent-*************
2025-06-28 01:38:08 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-MergeDataComponent-************* (total: 1)
2025-06-28 01:38:08 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'output_data': {'result': ['a', 'b', 'c', 'hello']}
2025-06-28 01:38:08 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-28 01:38:08 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle output_data: ['a', 'b', 'c', 'hello']
2025-06-28 01:38:08 - WorkflowUtils - DEBUG - Found result.result: ['a', 'b', 'c', 'hello'] (type: <class 'list'>)
2025-06-28 01:38:08 - WorkflowUtils - DEBUG - Searching for handle 'output_data' in list with 4 elements
2025-06-28 01:38:08 - WorkflowUtils - DEBUG - Handle 'output_data' not found in any list elements
2025-06-28 01:38:08 - WorkflowUtils - DEBUG - No handle matches found for 'output_data', treating result as single-value
2025-06-28 01:38:08 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 01:38:08 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 01:38:08 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'output_data': {'result': ['a', 'b', 'c', 'hello']}
2025-06-28 01:38:08 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-28 01:38:08 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle output_data: ['a', 'b', 'c', 'hello']
2025-06-28 01:38:08 - WorkflowUtils - DEBUG - Found result.result: ['a', 'b', 'c', 'hello'] (type: <class 'list'>)
2025-06-28 01:38:08 - WorkflowUtils - DEBUG - Searching for handle 'output_data' in list with 4 elements
2025-06-28 01:38:08 - WorkflowUtils - DEBUG - Handle 'output_data' not found in any list elements
2025-06-28 01:38:08 - WorkflowUtils - DEBUG - No handle matches found for 'output_data', treating result as single-value
2025-06-28 01:38:08 - WorkflowUtils - DEBUG - ✅ Handle mapping success: output_data → iteration_list via path 'result': ['a', 'b', 'c', 'hello']
2025-06-28 01:38:08 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 01:38:08 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 01:38:08 - WorkflowUtils - DEBUG - Filtering out field 'iteration_list' with null/empty value: None
2025-06-28 01:38:08 - WorkflowUtils - INFO - 🧹 Parameter filtering: 14 → 13 fields (1 null/empty fields removed)
2025-06-28 01:38:08 - TransitionHandler - DEBUG - 📌 Added static parameter: source_type = iteration_list
2025-06-28 01:38:08 - TransitionHandler - DEBUG - 📌 Added static parameter: batch_size = 1
2025-06-28 01:38:08 - TransitionHandler - DEBUG - 📌 Added static parameter: start = 1
2025-06-28 01:38:08 - TransitionHandler - DEBUG - 📌 Added static parameter: end = 3
2025-06-28 01:38:08 - TransitionHandler - DEBUG - 📌 Added static parameter: step = 1
2025-06-28 01:38:08 - TransitionHandler - DEBUG - 📌 Added static parameter: parallel_execution = True
2025-06-28 01:38:08 - TransitionHandler - DEBUG - 📌 Added static parameter: max_concurrent = 3
2025-06-28 01:38:08 - TransitionHandler - DEBUG - 📌 Added static parameter: preserve_order = True
2025-06-28 01:38:08 - TransitionHandler - DEBUG - 📌 Added static parameter: iteration_timeout = 60
2025-06-28 01:38:08 - TransitionHandler - DEBUG - 📌 Added static parameter: aggregation_type = collect_all
2025-06-28 01:38:08 - TransitionHandler - DEBUG - 📌 Added static parameter: include_metadata = True
2025-06-28 01:38:08 - TransitionHandler - DEBUG - 📌 Added static parameter: on_iteration_error = continue
2025-06-28 01:38:08 - TransitionHandler - DEBUG - 📌 Added static parameter: include_errors = True
2025-06-28 01:38:08 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'iteration_list': ['a', 'b', 'c', 'hello'], 'source_type': 'iteration_list', 'batch_size': '1', 'start': '1', 'end': '3', 'step': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 01:38:08 - TransitionHandler - DEBUG - tool Parameters: {'iteration_list': ['a', 'b', 'c', 'hello'], 'source_type': 'iteration_list', 'batch_size': '1', 'start': '1', 'end': '3', 'step': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 01:38:08 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'iteration_list': ['a', 'b', 'c', 'hello'], 'source_type': 'iteration_list', 'batch_size': '1', 'start': '1', 'end': '3', 'step': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 01:38:08 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id 867d9536-c215-4e26-a4f4-2713d7076ca7):
2025-06-28 01:38:08 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 867d9536-c215-4e26-a4f4-2713d7076ca7, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 5, 'workflow_status': 'running'}
2025-06-28 01:38:08 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-28 01:38:08 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-28 01:38:08 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-28 01:38:08 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-************* (has final/aggregated indicators)
2025-06-28 01:38:08 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-28 01:38:08 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-28 01:38:08 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: 1
2025-06-28 01:38:08 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-28 01:38:09 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-28 01:38:09 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:38:09 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-28 01:38:09 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'transition-MergeDataComponent-*************'}
2025-06-28 01:38:09 - StateManager - DEBUG - Stored result for transition current_iteration in memory: 1
2025-06-28 01:38:09 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-28 01:38:09 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-28 01:38:09 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:38:09 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-28 01:38:09 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'transition-MergeDataComponent-*************', 'current_iteration'}
2025-06-28 01:38:09 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 243331.406485458, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 01:38:10 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 01:38:10 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 01:38:10 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:38:10 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 01:38:10 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'transition-MergeDataComponent-*************', 'transition-LoopNode-*************', 'current_iteration'}
2025-06-28 01:38:10 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-28 01:38:10 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 6, corr_id 867d9536-c215-4e26-a4f4-2713d7076ca7):
2025-06-28 01:38:10 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 867d9536-c215-4e26-a4f4-2713d7076ca7, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 6, 'workflow_status': 'running'}
2025-06-28 01:38:10 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-28 01:38:10 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 01:38:10 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-28 01:38:10 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 01:38:10 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 01:38:11 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 01:38:11 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-28 01:38:11 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 01:38:11 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 01:38:11 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 243331.406485458, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 01:38:11 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 01:38:11 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 01:38:11 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 01:38:11 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 01:38:11 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 243331.406485458, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 01:38:11 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 01:38:11 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 01:38:11 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': 1
2025-06-28 01:38:11 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 01:38:11 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 01:38:11 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-28 01:38:11 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-28 01:38:11 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-28 01:38:11 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-28 01:38:11 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-28 01:38:11 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-28 01:38:11 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-28 01:38:11 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-28 01:38:11 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-28 01:38:11 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 4 fields (9 null/empty fields removed)
2025-06-28 01:38:11 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 01:38:11 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-06-28 01:38:11 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = coming
2025-06-28 01:38:11 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 1, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 01:38:11 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 1, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 01:38:11 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 1, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 01:38:11 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 7, corr_id 867d9536-c215-4e26-a4f4-2713d7076ca7):
2025-06-28 01:38:11 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 867d9536-c215-4e26-a4f4-2713d7076ca7, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 7, 'workflow_status': 'running'}
2025-06-28 01:38:11 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 8f8cee69-bbaa-4970-9309-7ca8eb1a107a) using provided producer.
2025-06-28 01:38:11 - NodeExecutor - DEBUG - Added correlation_id 867d9536-c215-4e26-a4f4-2713d7076ca7 to payload
2025-06-28 01:38:11 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-28 01:38:11 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 1, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}, 'request_id': '8f8cee69-bbaa-4970-9309-7ca8eb1a107a', 'correlation_id': '867d9536-c215-4e26-a4f4-2713d7076ca7', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-28 01:38:11 - NodeExecutor - DEBUG - Request 8f8cee69-bbaa-4970-9309-7ca8eb1a107a sent successfully using provided producer.
2025-06-28 01:38:11 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 8f8cee69-bbaa-4970-9309-7ca8eb1a107a...
2025-06-28 01:38:12 - NodeExecutor - DEBUG - Result consumer received message: Offset=813
2025-06-28 01:38:12 - NodeExecutor - DEBUG - Received valid result for request_id 8f8cee69-bbaa-4970-9309-7ca8eb1a107a
2025-06-28 01:38:12 - NodeExecutor - INFO - Result received for request 8f8cee69-bbaa-4970-9309-7ca8eb1a107a.
2025-06-28 01:38:12 - TransitionHandler - INFO - Execution result from Components executor: "1\ncoming"
2025-06-28 01:38:12 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 8, corr_id 867d9536-c215-4e26-a4f4-2713d7076ca7):
2025-06-28 01:38:12 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 867d9536-c215-4e26-a4f4-2713d7076ca7, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': '1\ncoming', 'status': 'completed', 'sequence': 8, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 01:38:12 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': '1\ncoming'}, 'status': 'completed', 'timestamp': 1751054892.154442}}
2025-06-28 01:38:12 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-28 01:38:13 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-28 01:38:13 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:38:13 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 01:38:13 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'transition-LoopNode-*************', 'transition-CombineTextComponent-*************', 'loop_iteration_0', 'transition-MergeDataComponent-*************'}
2025-06-28 01:38:13 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-28 01:38:13 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-28 01:38:13 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-28 01:38:13 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 01:38:13 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-28 01:38:13 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-28 01:38:13 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 01:38:13 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 01:38:13 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-28 01:38:13 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.30 seconds
2025-06-28 01:38:13 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 9, corr_id 867d9536-c215-4e26-a4f4-2713d7076ca7):
2025-06-28 01:38:13 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 867d9536-c215-4e26-a4f4-2713d7076ca7, response: {'result': 'Completed transition in 2.30 seconds', 'message': 'Transition completed in 2.30 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 9, 'workflow_status': 'running'}
2025-06-28 01:38:13 - StateManager - DEBUG - Stored result for transition loop_iteration_1 in memory: 2
2025-06-28 01:38:13 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_1
2025-06-28 01:38:13 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:38:13 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:38:13 - RedisManager - DEBUG - Set key 'result:loop_iteration_1' with TTL of 900 seconds
2025-06-28 01:38:13 - StateManager - DEBUG - Stored result for transition loop_iteration_1 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:38:13 - StateManager - INFO - Marked transition loop_iteration_1 as completed (was_pending=False, was_waiting=False)
2025-06-28 01:38:13 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_1', 'transition-CombineTextComponent-*************', 'loop_iteration_0', 'transition-MergeDataComponent-*************'}
2025-06-28 01:38:13 - StateManager - DEBUG - Stored result for transition current_iteration in memory: 2
2025-06-28 01:38:13 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:38:13 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:38:14 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-28 01:38:14 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-28 01:38:14 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:38:14 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-28 01:38:14 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_1', 'transition-CombineTextComponent-*************', 'loop_iteration_0', 'transition-MergeDataComponent-*************'}
2025-06-28 01:38:14 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 2, 'iteration_index': 1, 'iteration_metadata': {'timestamp': 243336.105979083, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 01:38:15 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 01:38:15 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 01:38:15 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:38:15 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 01:38:15 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_1', 'transition-CombineTextComponent-*************', 'loop_iteration_0', 'transition-MergeDataComponent-*************'}
2025-06-28 01:38:15 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-28 01:38:15 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 10, corr_id 867d9536-c215-4e26-a4f4-2713d7076ca7):
2025-06-28 01:38:15 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 867d9536-c215-4e26-a4f4-2713d7076ca7, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 10, 'workflow_status': 'running'}
2025-06-28 01:38:15 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-28 01:38:15 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 01:38:15 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-28 01:38:15 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 01:38:15 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 01:38:16 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 01:38:16 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-28 01:38:16 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 01:38:16 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 01:38:16 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 2, 'iteration_index': 1, 'iteration_metadata': {'timestamp': 243336.105979083, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 01:38:16 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 01:38:16 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 01:38:16 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 01:38:16 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 01:38:16 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 2, 'iteration_index': 1, 'iteration_metadata': {'timestamp': 243336.105979083, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 01:38:16 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 01:38:16 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 01:38:16 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': 2
2025-06-28 01:38:16 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 01:38:16 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 01:38:16 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-28 01:38:16 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-28 01:38:16 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-28 01:38:16 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-28 01:38:16 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-28 01:38:16 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-28 01:38:16 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-28 01:38:16 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-28 01:38:16 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-28 01:38:16 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 4 fields (9 null/empty fields removed)
2025-06-28 01:38:16 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 01:38:16 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-06-28 01:38:16 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = coming
2025-06-28 01:38:16 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 2, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 01:38:16 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 2, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 01:38:16 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 2, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 01:38:16 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 11, corr_id 867d9536-c215-4e26-a4f4-2713d7076ca7):
2025-06-28 01:38:16 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 867d9536-c215-4e26-a4f4-2713d7076ca7, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 11, 'workflow_status': 'running'}
2025-06-28 01:38:16 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 54cca1a5-a46b-4019-822c-d92baaafab80) using provided producer.
2025-06-28 01:38:16 - NodeExecutor - DEBUG - Added correlation_id 867d9536-c215-4e26-a4f4-2713d7076ca7 to payload
2025-06-28 01:38:16 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-28 01:38:16 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 2, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}, 'request_id': '54cca1a5-a46b-4019-822c-d92baaafab80', 'correlation_id': '867d9536-c215-4e26-a4f4-2713d7076ca7', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-28 01:38:16 - NodeExecutor - DEBUG - Request 54cca1a5-a46b-4019-822c-d92baaafab80 sent successfully using provided producer.
2025-06-28 01:38:16 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 54cca1a5-a46b-4019-822c-d92baaafab80...
2025-06-28 01:38:17 - NodeExecutor - DEBUG - Result consumer received message: Offset=814
2025-06-28 01:38:17 - NodeExecutor - DEBUG - Received valid result for request_id 54cca1a5-a46b-4019-822c-d92baaafab80
2025-06-28 01:38:17 - NodeExecutor - INFO - Result received for request 54cca1a5-a46b-4019-822c-d92baaafab80.
2025-06-28 01:38:17 - TransitionHandler - INFO - Execution result from Components executor: "2\ncoming"
2025-06-28 01:38:17 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 12, corr_id 867d9536-c215-4e26-a4f4-2713d7076ca7):
2025-06-28 01:38:17 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 867d9536-c215-4e26-a4f4-2713d7076ca7, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': '2\ncoming', 'status': 'completed', 'sequence': 12, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 01:38:17 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': '2\ncoming'}, 'status': 'completed', 'timestamp': 1751054897.145493}}
2025-06-28 01:38:17 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-28 01:38:18 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-28 01:38:18 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:38:18 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 01:38:18 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_1', 'transition-CombineTextComponent-*************', 'loop_iteration_0', 'transition-MergeDataComponent-*************'}
2025-06-28 01:38:18 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-28 01:38:18 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-28 01:38:18 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-28 01:38:18 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 01:38:18 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-28 01:38:18 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-28 01:38:18 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 01:38:18 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 01:38:18 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-28 01:38:18 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.33 seconds
2025-06-28 01:38:18 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 13, corr_id 867d9536-c215-4e26-a4f4-2713d7076ca7):
2025-06-28 01:38:18 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 867d9536-c215-4e26-a4f4-2713d7076ca7, response: {'result': 'Completed transition in 2.33 seconds', 'message': 'Transition completed in 2.33 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 13, 'workflow_status': 'running'}
2025-06-28 01:38:18 - StateManager - DEBUG - Stored result for transition loop_iteration_2 in memory: 3
2025-06-28 01:38:18 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_2
2025-06-28 01:38:18 - RedisManager - DEBUG - Set key 'result:loop_iteration_2' with TTL of 900 seconds
2025-06-28 01:38:18 - StateManager - DEBUG - Stored result for transition loop_iteration_2 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:38:18 - StateManager - INFO - Marked transition loop_iteration_2 as completed (was_pending=False, was_waiting=False)
2025-06-28 01:38:18 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_2', 'transition-LoopNode-*************', 'loop_iteration_1', 'transition-CombineTextComponent-*************', 'loop_iteration_0', 'transition-MergeDataComponent-*************'}
2025-06-28 01:38:18 - StateManager - DEBUG - Stored result for transition current_iteration in memory: 3
2025-06-28 01:38:19 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-28 01:38:19 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-28 01:38:19 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:38:19 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-28 01:38:19 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_2', 'transition-LoopNode-*************', 'loop_iteration_1', 'transition-CombineTextComponent-*************', 'loop_iteration_0', 'transition-MergeDataComponent-*************'}
2025-06-28 01:38:19 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 3, 'iteration_index': 2, 'iteration_metadata': {'timestamp': 243341.106995916, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 01:38:20 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 01:38:20 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 01:38:20 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:38:20 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 01:38:20 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_2', 'transition-LoopNode-*************', 'loop_iteration_1', 'transition-CombineTextComponent-*************', 'loop_iteration_0', 'transition-MergeDataComponent-*************'}
2025-06-28 01:38:20 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-28 01:38:20 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 14, corr_id 867d9536-c215-4e26-a4f4-2713d7076ca7):
2025-06-28 01:38:20 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 867d9536-c215-4e26-a4f4-2713d7076ca7, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 14, 'workflow_status': 'running'}
2025-06-28 01:38:20 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-28 01:38:20 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 01:38:20 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-28 01:38:20 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 01:38:20 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 01:38:21 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 01:38:21 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-28 01:38:21 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 01:38:21 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 01:38:21 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 3, 'iteration_index': 2, 'iteration_metadata': {'timestamp': 243341.106995916, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 01:38:21 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 01:38:21 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 01:38:21 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 01:38:21 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 01:38:21 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 3, 'iteration_index': 2, 'iteration_metadata': {'timestamp': 243341.106995916, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 01:38:21 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 01:38:21 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 01:38:21 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': 3
2025-06-28 01:38:21 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 01:38:21 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 01:38:21 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-28 01:38:21 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-28 01:38:21 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-28 01:38:21 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-28 01:38:21 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-28 01:38:21 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-28 01:38:21 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-28 01:38:21 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-28 01:38:21 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-28 01:38:21 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 4 fields (9 null/empty fields removed)
2025-06-28 01:38:21 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 01:38:21 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-06-28 01:38:21 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = coming
2025-06-28 01:38:21 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 3, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 01:38:21 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 3, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 01:38:21 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 3, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}
2025-06-28 01:38:21 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 15, corr_id 867d9536-c215-4e26-a4f4-2713d7076ca7):
2025-06-28 01:38:21 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 867d9536-c215-4e26-a4f4-2713d7076ca7, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 15, 'workflow_status': 'running'}
2025-06-28 01:38:21 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 5d87021d-8f74-43fe-aa0d-94bedf643cc2) using provided producer.
2025-06-28 01:38:21 - NodeExecutor - DEBUG - Added correlation_id 867d9536-c215-4e26-a4f4-2713d7076ca7 to payload
2025-06-28 01:38:21 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-28 01:38:21 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 3, 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'coming'}, 'request_id': '5d87021d-8f74-43fe-aa0d-94bedf643cc2', 'correlation_id': '867d9536-c215-4e26-a4f4-2713d7076ca7', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-28 01:38:21 - NodeExecutor - DEBUG - Request 5d87021d-8f74-43fe-aa0d-94bedf643cc2 sent successfully using provided producer.
2025-06-28 01:38:21 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 5d87021d-8f74-43fe-aa0d-94bedf643cc2...
2025-06-28 01:38:22 - NodeExecutor - DEBUG - Result consumer received message: Offset=815
2025-06-28 01:38:22 - NodeExecutor - DEBUG - Received valid result for request_id 5d87021d-8f74-43fe-aa0d-94bedf643cc2
2025-06-28 01:38:22 - NodeExecutor - INFO - Result received for request 5d87021d-8f74-43fe-aa0d-94bedf643cc2.
2025-06-28 01:38:22 - TransitionHandler - INFO - Execution result from Components executor: "3\ncoming"
2025-06-28 01:38:22 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 16, corr_id 867d9536-c215-4e26-a4f4-2713d7076ca7):
2025-06-28 01:38:22 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 867d9536-c215-4e26-a4f4-2713d7076ca7, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': '3\ncoming', 'status': 'completed', 'sequence': 16, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 01:38:22 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': '3\ncoming'}, 'status': 'completed', 'timestamp': 1751054902.1157298}}
2025-06-28 01:38:22 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-28 01:38:22 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-28 01:38:22 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:38:22 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 01:38:22 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_2', 'transition-LoopNode-*************', 'loop_iteration_1', 'transition-CombineTextComponent-*************', 'loop_iteration_0', 'transition-MergeDataComponent-*************'}
2025-06-28 01:38:22 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-28 01:38:22 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-28 01:38:22 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-28 01:38:22 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 01:38:22 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-28 01:38:22 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-28 01:38:22 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 01:38:22 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 01:38:22 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-28 01:38:22 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.38 seconds
2025-06-28 01:38:22 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 17, corr_id 867d9536-c215-4e26-a4f4-2713d7076ca7):
2025-06-28 01:38:22 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 867d9536-c215-4e26-a4f4-2713d7076ca7, response: {'result': 'Completed transition in 2.38 seconds', 'message': 'Transition completed in 2.38 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 17, 'workflow_status': 'running'}
2025-06-28 01:38:22 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 18, corr_id 867d9536-c215-4e26-a4f4-2713d7076ca7):
2025-06-28 01:38:22 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 867d9536-c215-4e26-a4f4-2713d7076ca7, response: {'status': 'loop_completed', 'metadata': {'success': True, 'final_results': ['1\ncoming', '2\ncoming', '3\ncoming'], 'iteration_count': 3, 'total_iterations': 3}, 'transition_id': 'transition-LoopNode-*************', 'sequence': 18, 'workflow_status': 'running'}
2025-06-28 01:38:22 - TransitionHandler - DEBUG - 🗑️ Unregistered loop executor for transition: transition-LoopNode-*************
2025-06-28 01:38:22 - TransitionHandler - INFO - Execution result from loop executor: {
  "final_results": [
    "1\ncoming",
    "2\ncoming",
    "3\ncoming"
  ]
}
2025-06-28 01:38:22 - TransitionHandler - INFO - Checking execution result for errors: {
  "final_results": [
    "1\ncoming",
    "2\ncoming",
    "3\ncoming"
  ]
}
2025-06-28 01:38:22 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 19, corr_id 867d9536-c215-4e26-a4f4-2713d7076ca7):
2025-06-28 01:38:22 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 867d9536-c215-4e26-a4f4-2713d7076ca7, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Transition Result received.', 'result': {'final_results': ['1\ncoming', '2\ncoming', '3\ncoming']}, 'status': 'completed', 'sequence': 19, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 01:38:22 - TransitionHandler - INFO - 🔄 Handling loop completion for transition: transition-LoopNode-*************
2025-06-28 01:38:22 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'final_results': ['1\ncoming', '2\ncoming', '3\ncoming']}
2025-06-28 01:38:23 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 01:38:23 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 01:38:23 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:38:23 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 01:38:23 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_2', 'transition-LoopNode-*************', 'loop_iteration_1', 'transition-CombineTextComponent-*************', 'loop_iteration_0', 'transition-MergeDataComponent-*************'}
2025-06-28 01:38:23 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-28 01:38:23 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-************* (has final/aggregated indicators)
2025-06-28 01:38:23 - TransitionHandler - INFO - 🔍 Auto-detected loop body transitions: ['transition-CombineTextComponent-*************']
2025-06-28 01:38:23 - TransitionHandler - DEBUG - 🔍 Loop completion filtering - Loop config: {'iteration_behavior': 'independent', 'iteration_source': {'number_range': {'start': 1, 'end': 3, 'step': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}}
2025-06-28 01:38:23 - TransitionHandler - DEBUG - 🔍 Loop body transitions to filter: ['transition-CombineTextComponent-*************']
2025-06-28 01:38:23 - TransitionHandler - DEBUG - 🔍 Output data configs: [{'to_transition_id': 'transition-CombineTextComponent-*************', 'target_node_id': 'Combine Text', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'current_item', 'result_path': 'current_item', 'edge_id': 'reactflow__edge-LoopNode-*************current_item-CombineTextComponent-*************main_input'}]}}, {'to_transition_id': 'transition-MergeDataComponent-*************', 'target_node_id': 'Merge Data', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'final_results', 'result_path': 'final_results', 'edge_id': 'reactflow__edge-LoopNode-*************final_results-MergeDataComponent-*************main_input'}]}}]
2025-06-28 01:38:23 - TransitionHandler - DEBUG - 🚫 Skipping loop body transition 'transition-CombineTextComponent-*************' - should have been executed internally by loop executor
2025-06-28 01:38:23 - TransitionHandler - DEBUG - ✅ Adding exit transition 'transition-MergeDataComponent-*************' to next transitions
2025-06-28 01:38:23 - TransitionHandler - INFO - ✅ Loop completion handled for transition-LoopNode-*************. Next transitions: ['transition-MergeDataComponent-*************']
2025-06-28 01:38:23 - TransitionHandler - INFO - Completed transition transition-LoopNode-************* in 16.71 seconds
2025-06-28 01:38:23 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 20, corr_id 867d9536-c215-4e26-a4f4-2713d7076ca7):
2025-06-28 01:38:23 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 867d9536-c215-4e26-a4f4-2713d7076ca7, response: {'result': 'Completed transition in 16.71 seconds', 'message': 'Transition completed in 16.71 seconds', 'transition_id': 'transition-LoopNode-*************', 'status': 'time_logged', 'sequence': 20, 'workflow_status': 'running'}
2025-06-28 01:38:23 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-MergeDataComponent-*************']]
2025-06-28 01:38:23 - EnhancedWorkflowEngine - INFO - Transition transition-LoopNode-************* completed successfully: 1 next transitions
2025-06-28 01:38:23 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-MergeDataComponent-*************']
2025-06-28 01:38:23 - EnhancedWorkflowEngine - INFO - Adding transition transition-MergeDataComponent-************* to pending (all dependencies met)
2025-06-28 01:38:23 - StateManager - DEBUG - Workflow active: {'transition-MergeDataComponent-*************'}
2025-06-28 01:38:24 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:867d9536-c215-4e26-a4f4-2713d7076ca7'
2025-06-28 01:38:24 - RedisManager - DEBUG - Set key 'workflow_state:867d9536-c215-4e26-a4f4-2713d7076ca7' with TTL of 600 seconds
2025-06-28 01:38:24 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 867d9536-c215-4e26-a4f4-2713d7076ca7. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:38:24 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 01:38:24 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 01:38:24 - StateManager - INFO - Cleared 1 pending transitions: {'transition-MergeDataComponent-*************'}
2025-06-28 01:38:24 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 01:38:24 - StateManager - INFO - Terminated: False
2025-06-28 01:38:24 - StateManager - INFO - Pending transitions (0): []
2025-06-28 01:38:24 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 01:38:24 - StateManager - INFO - Completed transitions (7): ['current_iteration', 'loop_iteration_0', 'loop_iteration_1', 'loop_iteration_2', 'transition-CombineTextComponent-*************', 'transition-LoopNode-*************', 'transition-MergeDataComponent-*************']
2025-06-28 01:38:24 - StateManager - INFO - Results stored for 7 transitions
2025-06-28 01:38:24 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 01:38:24 - StateManager - INFO - Workflow status: inactive
2025-06-28 01:38:24 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 01:38:24 - StateManager - INFO - Workflow status: inactive
2025-06-28 01:38:24 - StateManager - INFO - Workflow paused: False
2025-06-28 01:38:24 - StateManager - INFO - ==============================
2025-06-28 01:38:24 - TransitionHandler - INFO - Starting parallel execution of transition: transition-MergeDataComponent-*************
2025-06-28 01:38:24 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 21, corr_id 867d9536-c215-4e26-a4f4-2713d7076ca7):
2025-06-28 01:38:24 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 867d9536-c215-4e26-a4f4-2713d7076ca7, response: {'result': 'Starting execution of transition: transition-MergeDataComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'started', 'sequence': 21, 'workflow_status': 'running'}
2025-06-28 01:38:24 - TransitionHandler - EXECUTE - Transition 'transition-MergeDataComponent-*************' (type=standard, execution_type=Components)
2025-06-28 01:38:24 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 01:38:24 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-MergeDataComponent-*************
2025-06-28 01:38:24 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 01:38:24 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 01:38:25 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 01:38:25 - StateManager - DEBUG - Extracted results for 1 tools in transition transition-LoopNode-*************
2025-06-28 01:38:25 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 01:38:25 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 01:38:25 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_results': {'final_results': ['1\ncoming', '2\ncoming', '3\ncoming']}
2025-06-28 01:38:25 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-28 01:38:25 - WorkflowUtils - DEBUG - Path tracking - Found handle 'final_results' directly in source_results
2025-06-28 01:38:25 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 01:38:25 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 01:38:25 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_results': {'final_results': ['1\ncoming', '2\ncoming', '3\ncoming']}
2025-06-28 01:38:25 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-28 01:38:25 - WorkflowUtils - DEBUG - Path tracking - Found handle 'final_results' directly in source_results
2025-06-28 01:38:25 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_results → main_input via path 'final_results': ['1\ncoming', '2\ncoming', '3\ncoming']
2025-06-28 01:38:25 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 01:38:25 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 01:38:25 - WorkflowUtils - DEBUG - Filtering out field 'output_key_1' with null/empty value: 
2025-06-28 01:38:25 - WorkflowUtils - DEBUG - Filtering out field 'output_key_2' with null/empty value: 
2025-06-28 01:38:25 - WorkflowUtils - DEBUG - Filtering out field 'output_key_3' with null/empty value: 
2025-06-28 01:38:25 - WorkflowUtils - DEBUG - Filtering out field 'output_key_4' with null/empty value: 
2025-06-28 01:38:25 - WorkflowUtils - DEBUG - Filtering out field 'output_key_5' with null/empty value: 
2025-06-28 01:38:25 - WorkflowUtils - DEBUG - Filtering out field 'output_key_6' with null/empty value: 
2025-06-28 01:38:25 - WorkflowUtils - DEBUG - Filtering out field 'output_key_7' with null/empty value: 
2025-06-28 01:38:25 - WorkflowUtils - DEBUG - Filtering out field 'output_key_8' with null/empty value: 
2025-06-28 01:38:25 - WorkflowUtils - DEBUG - Filtering out field 'output_key_9' with null/empty value: 
2025-06-28 01:38:25 - WorkflowUtils - DEBUG - Filtering out field 'output_key_10' with null/empty value: 
2025-06-28 01:38:25 - WorkflowUtils - DEBUG - Filtering out field 'output_key_11' with null/empty value: 
2025-06-28 01:38:25 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with empty collection: {}
2025-06-28 01:38:25 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with empty collection: {}
2025-06-28 01:38:25 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with empty collection: {}
2025-06-28 01:38:25 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with empty collection: {}
2025-06-28 01:38:25 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with empty collection: {}
2025-06-28 01:38:25 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with empty collection: {}
2025-06-28 01:38:25 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with empty collection: {}
2025-06-28 01:38:25 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with empty collection: {}
2025-06-28 01:38:25 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with empty collection: {}
2025-06-28 01:38:25 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with empty collection: {}
2025-06-28 01:38:25 - WorkflowUtils - INFO - 🧹 Parameter filtering: 24 → 3 fields (21 null/empty fields removed)
2025-06-28 01:38:25 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 01:38:25 - TransitionHandler - DEBUG - 📌 Added static parameter: merge_strategy = Aggregate
2025-06-28 01:38:25 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': ['1\ncoming', '2\ncoming', '3\ncoming'], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-06-28 01:38:25 - TransitionHandler - DEBUG - tool Parameters: {'main_input': ['1\ncoming', '2\ncoming', '3\ncoming'], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-06-28 01:38:25 - TransitionHandler - INFO - Invoking tool 'MergeDataComponent' (tool_id: 1) for node 'MergeDataComponent' in transition 'transition-MergeDataComponent-*************' with parameters: {'main_input': ['1\ncoming', '2\ncoming', '3\ncoming'], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-06-28 01:38:25 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 22, corr_id 867d9536-c215-4e26-a4f4-2713d7076ca7):
2025-06-28 01:38:25 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 867d9536-c215-4e26-a4f4-2713d7076ca7, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'message': 'Connecting to server', 'result': 'Connecting to server MergeDataComponent', 'status': 'connecting', 'sequence': 22, 'workflow_status': 'running'}
2025-06-28 01:38:25 - NodeExecutor - INFO - Executing tool 'MergeDataComponent' via Kafka (request_id: 8dade297-e5a5-4cbc-a4af-a13a356c2e54) using provided producer.
2025-06-28 01:38:25 - NodeExecutor - DEBUG - Added correlation_id 867d9536-c215-4e26-a4f4-2713d7076ca7 to payload
2025-06-28 01:38:25 - NodeExecutor - DEBUG - Added transition_id transition-MergeDataComponent-************* to payload
2025-06-28 01:38:25 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'MergeDataComponent', 'tool_parameters': {'main_input': ['1\ncoming', '2\ncoming', '3\ncoming'], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}, 'request_id': '8dade297-e5a5-4cbc-a4af-a13a356c2e54', 'correlation_id': '867d9536-c215-4e26-a4f4-2713d7076ca7', 'transition_id': 'transition-MergeDataComponent-*************'}
2025-06-28 01:38:25 - NodeExecutor - DEBUG - Request 8dade297-e5a5-4cbc-a4af-a13a356c2e54 sent successfully using provided producer.
2025-06-28 01:38:25 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 8dade297-e5a5-4cbc-a4af-a13a356c2e54...
2025-06-28 01:38:26 - NodeExecutor - DEBUG - Result consumer received message: Offset=816
2025-06-28 01:38:26 - NodeExecutor - DEBUG - Received valid result for request_id 8dade297-e5a5-4cbc-a4af-a13a356c2e54
2025-06-28 01:38:26 - NodeExecutor - INFO - Result received for request 8dade297-e5a5-4cbc-a4af-a13a356c2e54.
2025-06-28 01:38:26 - TransitionHandler - INFO - Execution result from Components executor: [
  "1\ncoming",
  "2\ncoming",
  "3\ncoming"
]
2025-06-28 01:38:26 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 23, corr_id 867d9536-c215-4e26-a4f4-2713d7076ca7):
2025-06-28 01:38:26 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 867d9536-c215-4e26-a4f4-2713d7076ca7, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'message': 'Transition Result received.', 'result': ['1\ncoming', '2\ncoming', '3\ncoming'], 'status': 'completed', 'sequence': 23, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 01:38:26 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in memory: {'MergeDataComponent': {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'result': {'result': ['1\ncoming', '2\ncoming', '3\ncoming']}, 'status': 'completed', 'timestamp': 1751054906.33134}}
2025-06-28 01:38:26 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-MergeDataComponent-*************'
2025-06-28 01:38:27 - RedisManager - DEBUG - Set key 'result:transition-MergeDataComponent-*************' with TTL of 300 seconds
2025-06-28 01:38:27 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 01:38:27 - StateManager - INFO - Marked transition transition-MergeDataComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 01:38:27 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'transition-MergeDataComponent-*************', 'loop_iteration_2', 'transition-LoopNode-*************', 'loop_iteration_1', 'transition-CombineTextComponent-*************', 'loop_iteration_0', 'transition-MergeDataComponent-*************'}
2025-06-28 01:38:27 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-MergeDataComponent-*************
2025-06-28 01:38:27 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'list'>
2025-06-28 01:38:27 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 01:38:27 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'list'>
2025-06-28 01:38:27 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-MergeDataComponent-*************:
2025-06-28 01:38:27 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 01:38:27 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 01:38:27 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-MergeDataComponent-*************, returning empty list
2025-06-28 01:38:27 - TransitionHandler - INFO - Completed transition transition-MergeDataComponent-************* in 2.39 seconds
2025-06-28 01:38:27 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 24, corr_id 867d9536-c215-4e26-a4f4-2713d7076ca7):
2025-06-28 01:38:27 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 867d9536-c215-4e26-a4f4-2713d7076ca7, response: {'result': 'Completed transition in 2.39 seconds', 'message': 'Transition completed in 2.39 seconds', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'time_logged', 'sequence': 24, 'workflow_status': 'running'}
2025-06-28 01:38:27 - EnhancedWorkflowEngine - DEBUG - Results: [[]]
2025-06-28 01:38:27 - StateManager - INFO - Workflow terminated flag set to: True
2025-06-28 01:38:27 - EnhancedWorkflowEngine - INFO - Workflow is marked as terminated, ending workflow execution.
2025-06-28 01:38:27 - StateManager - DEBUG - Workflow active: False (terminated=True, pending=empty, waiting=empty)
2025-06-28 01:38:27 - EnhancedWorkflowEngine - INFO - Workflow execution completed.
2025-06-28 01:38:27 - KafkaWorkflowConsumer - INFO - Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' final status: completed, result: Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' executed successfully.
2025-06-28 01:38:27 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 867d9536-c215-4e26-a4f4-2713d7076ca7, response: {'status': 'complete', 'result': "Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' executed successfully.", 'workflow_status': 'completed'}
2025-06-28 01:38:27 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: 867d9536-c215-4e26-a4f4-2713d7076ca7 
2025-06-28 01:39:13 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:39:13 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:39:13 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:39:13 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:40:13 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:40:13 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:40:14 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:40:14 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:41:13 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:41:13 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:41:13 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:41:13 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:42:13 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:42:13 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:42:13 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:42:13 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:43:06 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-MergeDataComponent-*************', 'data': b'expired'}
2025-06-28 01:43:06 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-MergeDataComponent-*************'
2025-06-28 01:43:06 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-MergeDataComponent-*************
2025-06-28 01:43:06 - RedisEventListener - DEBUG - Extracted key: result:transition-MergeDataComponent-*************
2025-06-28 01:43:06 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-28 01:43:06 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-28 01:43:06 - RedisEventListener - INFO - Detected expired event for result of transition: transition-MergeDataComponent-*************
2025-06-28 01:43:06 - RedisEventListener - INFO - Archiving result for transition: transition-MergeDataComponent-*************
2025-06-28 01:43:06 - StateManager - DEBUG - Attempting to archive result for transition transition-MergeDataComponent-*************
2025-06-28 01:43:07 - StateManager - DEBUG - Provided result: False
2025-06-28 01:43:07 - StateManager - DEBUG - Trying to get result from Redis for transition transition-MergeDataComponent-*************
2025-06-28 01:43:08 - StateManager - DEBUG - No result found in Redis for transition transition-MergeDataComponent-*************
2025-06-28 01:43:08 - StateManager - DEBUG - Trying to get result from memory for transition transition-MergeDataComponent-*************
2025-06-28 01:43:08 - StateManager - DEBUG - Found result in memory for transition transition-MergeDataComponent-*************
2025-06-28 01:43:08 - StateManager - DEBUG - Archiving result to PostgreSQL for transition transition-MergeDataComponent-*************
2025-06-28 01:43:08 - PostgresManager - DEBUG - Attempting to store transition result for transition-MergeDataComponent-************* in correlation 867d9536-c215-4e26-a4f4-2713d7076ca7
2025-06-28 01:43:08 - PostgresManager - DEBUG - Result data type: <class 'dict'>
2025-06-28 01:43:08 - PostgresManager - DEBUG - Result data: {'MergeDataComponent': {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'result': {'result': ['a', 'b', 'c', 'hello']}, 'status': 'completed', 'timestamp': 1751054885.134186}}
2025-06-28 01:43:11 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 01:43:12 - PostgresManager - DEBUG - Successfully serialized result data to JSON string
2025-06-28 01:43:12 - PostgresManager - DEBUG - Converted to psycopg2 Json object
2025-06-28 01:43:12 - PostgresManager - DEBUG - Inserting new record for transition transition-MergeDataComponent-*************
2025-06-28 01:43:12 - PostgresManager - DEBUG - Inserted new result for transition transition-MergeDataComponent-************* in correlation 867d9536-c215-4e26-a4f4-2713d7076ca7
2025-06-28 01:43:12 - PostgresManager - DEBUG - Successfully stored transition result for transition-MergeDataComponent-*************
2025-06-28 01:43:12 - StateManager - INFO - Archived result for transition transition-MergeDataComponent-************* to PostgreSQL
2025-06-28 01:43:13 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:43:13 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:43:14 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:43:14 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:43:23 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-CombineTextComponent-*************', 'data': b'expired'}
2025-06-28 01:43:23 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-CombineTextComponent-*************'
2025-06-28 01:43:23 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-CombineTextComponent-*************
2025-06-28 01:43:23 - RedisEventListener - DEBUG - Extracted key: result:transition-CombineTextComponent-*************
2025-06-28 01:43:23 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-28 01:43:23 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-28 01:43:23 - RedisEventListener - INFO - Detected expired event for result of transition: transition-CombineTextComponent-*************
2025-06-28 01:43:23 - RedisEventListener - INFO - Archiving result for transition: transition-CombineTextComponent-*************
2025-06-28 01:43:23 - StateManager - DEBUG - Attempting to archive result for transition transition-CombineTextComponent-*************
2025-06-28 01:43:23 - StateManager - DEBUG - Provided result: False
2025-06-28 01:43:24 - StateManager - DEBUG - Trying to get result from Redis for transition transition-CombineTextComponent-*************
2025-06-28 01:43:24 - StateManager - DEBUG - No result found in Redis for transition transition-CombineTextComponent-*************
2025-06-28 01:43:24 - StateManager - DEBUG - Trying to get result from memory for transition transition-CombineTextComponent-*************
2025-06-28 01:43:24 - StateManager - DEBUG - Found result in memory for transition transition-CombineTextComponent-*************
2025-06-28 01:43:24 - StateManager - DEBUG - Archiving result to PostgreSQL for transition transition-CombineTextComponent-*************
2025-06-28 01:43:24 - PostgresManager - DEBUG - Attempting to store transition result for transition-CombineTextComponent-************* in correlation 867d9536-c215-4e26-a4f4-2713d7076ca7
2025-06-28 01:43:24 - PostgresManager - DEBUG - Result data type: <class 'dict'>
2025-06-28 01:43:24 - PostgresManager - DEBUG - Result data: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': '3\ncoming'}, 'status': 'completed', 'timestamp': 1751054902.1157298}}
2025-06-28 01:43:28 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 01:43:29 - PostgresManager - DEBUG - Successfully serialized result data to JSON string
2025-06-28 01:43:29 - PostgresManager - DEBUG - Converted to psycopg2 Json object
2025-06-28 01:43:29 - PostgresManager - DEBUG - Inserting new record for transition transition-CombineTextComponent-*************
2025-06-28 01:43:29 - PostgresManager - DEBUG - Inserted new result for transition transition-CombineTextComponent-************* in correlation 867d9536-c215-4e26-a4f4-2713d7076ca7
2025-06-28 01:43:29 - PostgresManager - DEBUG - Successfully stored transition result for transition-CombineTextComponent-*************
2025-06-28 01:43:29 - StateManager - INFO - Archived result for transition transition-CombineTextComponent-************* to PostgreSQL
2025-06-28 01:43:29 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-LoopNode-*************', 'data': b'expired'}
2025-06-28 01:43:29 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-LoopNode-*************'
2025-06-28 01:43:29 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-LoopNode-*************
2025-06-28 01:43:29 - RedisEventListener - DEBUG - Extracted key: result:transition-LoopNode-*************
2025-06-28 01:43:29 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-28 01:43:29 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-28 01:43:29 - RedisEventListener - INFO - Detected expired event for result of transition: transition-LoopNode-*************
2025-06-28 01:43:29 - RedisEventListener - INFO - Archiving result for transition: transition-LoopNode-*************
2025-06-28 01:43:29 - StateManager - DEBUG - Attempting to archive result for transition transition-LoopNode-*************
2025-06-28 01:43:30 - StateManager - DEBUG - Provided result: False
2025-06-28 01:43:30 - StateManager - DEBUG - Trying to get result from Redis for transition transition-LoopNode-*************
2025-06-28 01:43:31 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************
2025-06-28 01:43:31 - StateManager - DEBUG - Trying to get result from memory for transition transition-LoopNode-*************
2025-06-28 01:43:31 - StateManager - DEBUG - Found result in memory for transition transition-LoopNode-*************
2025-06-28 01:43:31 - StateManager - DEBUG - Archiving result to PostgreSQL for transition transition-LoopNode-*************
2025-06-28 01:43:31 - PostgresManager - DEBUG - Attempting to store transition result for transition-LoopNode-************* in correlation 867d9536-c215-4e26-a4f4-2713d7076ca7
2025-06-28 01:43:31 - PostgresManager - DEBUG - Result data type: <class 'dict'>
2025-06-28 01:43:31 - PostgresManager - DEBUG - Result data: {'final_results': ['1\ncoming', '2\ncoming', '3\ncoming']}
2025-06-28 01:43:34 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 01:43:35 - PostgresManager - DEBUG - Successfully serialized result data to JSON string
2025-06-28 01:43:35 - PostgresManager - DEBUG - Converted to psycopg2 Json object
2025-06-28 01:43:35 - PostgresManager - DEBUG - Inserting new record for transition transition-LoopNode-*************
2025-06-28 01:43:35 - PostgresManager - DEBUG - Inserted new result for transition transition-LoopNode-************* in correlation 867d9536-c215-4e26-a4f4-2713d7076ca7
2025-06-28 01:43:35 - PostgresManager - DEBUG - Successfully stored transition result for transition-LoopNode-*************
2025-06-28 01:43:35 - StateManager - INFO - Archived result for transition transition-LoopNode-************* to PostgreSQL
2025-06-28 01:43:35 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-MergeDataComponent-*************', 'data': b'expired'}
2025-06-28 01:43:35 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-MergeDataComponent-*************'
2025-06-28 01:43:35 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-MergeDataComponent-*************
2025-06-28 01:43:35 - RedisEventListener - DEBUG - Extracted key: result:transition-MergeDataComponent-*************
2025-06-28 01:43:35 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-28 01:43:35 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-28 01:43:35 - RedisEventListener - INFO - Detected expired event for result of transition: transition-MergeDataComponent-*************
2025-06-28 01:43:35 - RedisEventListener - INFO - Archiving result for transition: transition-MergeDataComponent-*************
2025-06-28 01:43:35 - StateManager - DEBUG - Attempting to archive result for transition transition-MergeDataComponent-*************
2025-06-28 01:43:36 - StateManager - DEBUG - Provided result: False
2025-06-28 01:43:36 - StateManager - DEBUG - Trying to get result from Redis for transition transition-MergeDataComponent-*************
2025-06-28 01:43:37 - StateManager - DEBUG - No result found in Redis for transition transition-MergeDataComponent-*************
2025-06-28 01:43:37 - StateManager - DEBUG - Trying to get result from memory for transition transition-MergeDataComponent-*************
2025-06-28 01:43:37 - StateManager - DEBUG - Found result in memory for transition transition-MergeDataComponent-*************
2025-06-28 01:43:37 - StateManager - DEBUG - Archiving result to PostgreSQL for transition transition-MergeDataComponent-*************
2025-06-28 01:43:37 - PostgresManager - DEBUG - Attempting to store transition result for transition-MergeDataComponent-************* in correlation 867d9536-c215-4e26-a4f4-2713d7076ca7
2025-06-28 01:43:37 - PostgresManager - DEBUG - Result data type: <class 'dict'>
2025-06-28 01:43:37 - PostgresManager - DEBUG - Result data: {'MergeDataComponent': {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'result': {'result': ['1\ncoming', '2\ncoming', '3\ncoming']}, 'status': 'completed', 'timestamp': 1751054906.33134}}
2025-06-28 01:43:42 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 01:43:42 - PostgresManager - DEBUG - Successfully serialized result data to JSON string
2025-06-28 01:43:42 - PostgresManager - DEBUG - Converted to psycopg2 Json object
2025-06-28 01:43:42 - PostgresManager - DEBUG - Inserting new record for transition transition-MergeDataComponent-*************
2025-06-28 01:43:42 - PostgresManager - DEBUG - Inserted new result for transition transition-MergeDataComponent-************* in correlation 867d9536-c215-4e26-a4f4-2713d7076ca7
2025-06-28 01:43:43 - PostgresManager - DEBUG - Successfully stored transition result for transition-MergeDataComponent-*************
2025-06-28 01:43:43 - StateManager - INFO - Archived result for transition transition-MergeDataComponent-************* to PostgreSQL
2025-06-28 01:44:13 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:44:13 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:44:13 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:44:13 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:45:13 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:45:13 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:45:14 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:45:14 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:46:13 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:46:13 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:46:14 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:46:14 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:47:13 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:47:13 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:47:14 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:47:14 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:48:13 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:48:13 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:48:14 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:48:14 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 01:48:24 - RedisEventListener - DEBUG - Raw state DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@6__:*', 'channel': b'__keyspace@6__:workflow_state:867d9536-c215-4e26-a4f4-2713d7076ca7', 'data': b'expired'}
2025-06-28 01:48:24 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@6__:workflow_state:867d9536-c215-4e26-a4f4-2713d7076ca7'
2025-06-28 01:48:24 - RedisEventListener - DEBUG - Decoded channel: __keyspace@6__:workflow_state:867d9536-c215-4e26-a4f4-2713d7076ca7
2025-06-28 01:48:24 - RedisEventListener - DEBUG - Extracted key: workflow_state:867d9536-c215-4e26-a4f4-2713d7076ca7
2025-06-28 01:48:24 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-28 01:48:24 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-28 01:48:24 - RedisEventListener - INFO - Detected expired event for workflow state of workflow: 867d9536-c215-4e26-a4f4-2713d7076ca7
2025-06-28 01:48:24 - RedisEventListener - INFO - Archiving workflow state for workflow: 867d9536-c215-4e26-a4f4-2713d7076ca7
2025-06-28 01:48:28 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 01:48:29 - PostgresManager - DEBUG - Inserted new workflow state for correlation_id: 867d9536-c215-4e26-a4f4-2713d7076ca7
2025-06-28 01:48:30 - StateManager - INFO - Archived workflow state to PostgreSQL for workflow ID: 867d9536-c215-4e26-a4f4-2713d7076ca7
2025-06-28 01:49:13 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 01:49:14 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:49:14 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 01:49:14 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
