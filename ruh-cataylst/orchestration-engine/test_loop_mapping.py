#!/usr/bin/env python3

"""
Test script to verify that the intelligent loop parameter mapping works
with the user's original payload structure (using 'main_input' field name).
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from services.initialize_workflow import _update_loop_config_from_tool_params

def test_intelligent_mapping():
    """Test that the function can map from any field name to proper loop config"""
    
    # Simulate a transition with tool_params that has user input
    # This represents what happens after the user payload is processed
    transition = {
        "id": "MergeDataComponent-1751053030287",
        "execution_type": "loop",  # Required for the function to process it
        "node_info": {
            "tools_to_use": [{
                "tool_params": {
                    "items": [
                        {
                            "field_name": "main_input",  # User's field name
                            "field_value": ["a", "b", "c"],  # User's array value
                            "data_type": "array"
                        },
                        {
                            "field_name": "batch_size",
                            "field_value": 1,
                            "data_type": "number"
                        }
                    ]
                }
            }]
        },
        "loop_config": {}
    }
    
    print("Before mapping:")
    print(f"  tool_params: {transition['node_info']['tools_to_use'][0]['tool_params']}")
    print(f"  loop_config: {transition['loop_config']}")
    
    # Apply the intelligent mapping
    _update_loop_config_from_tool_params(transition)
    
    print("\nAfter mapping:")
    print(f"  loop_config: {transition['loop_config']}")
    
    # Verify the result
    expected_structure = {
        "iteration_source": {
            "iteration_list": ["a", "b", "c"],
            "batch_size": 1
        }
    }
    
    if transition["loop_config"] == expected_structure:
        print("\n✅ SUCCESS: Intelligent mapping worked correctly!")
        print("   - Detected list value in 'main_input' field")
        print("   - Created proper nested iteration_source structure")
        print("   - Field name mismatch was handled automatically")
        return True
    else:
        print("\n❌ FAILURE: Mapping did not work as expected")
        print(f"   Expected: {expected_structure}")
        print(f"   Got: {transition['loop_config']}")
        return False

def test_number_range_mapping():
    """Test intelligent mapping for number range parameters"""
    
    transition = {
        "id": "LoopNode-123",
        "execution_type": "loop",  # Required for the function to process it
        "node_info": {
            "tools_to_use": [{
                "tool_params": {
                    "items": [
                        {
                            "field_name": "range_start",  # Different field name
                            "field_value": "1",
                            "data_type": "number"
                        },
                        {
                            "field_name": "range_end",  # Different field name
                            "field_value": "5",
                            "data_type": "number"
                        },
                        {
                            "field_name": "step_size",  # Different field name
                            "field_value": "2",
                            "data_type": "number"
                        }
                    ]
                }
            }]
        },
        "loop_config": {}
    }
    
    print("\n" + "="*50)
    print("Testing number range mapping...")
    print("Before mapping:")
    print(f"  tool_params: {transition['node_info']['tools_to_use'][0]['tool_params']}")
    print(f"  loop_config: {transition['loop_config']}")
    
    # Apply the intelligent mapping
    _update_loop_config_from_tool_params(transition)
    
    print("\nAfter mapping:")
    print(f"  loop_config: {transition['loop_config']}")
    
    # For number range, we need start/end field names to contain those keywords
    # Let's test with proper field names
    transition2 = {
        "id": "LoopNode-456",
        "execution_type": "loop",  # Required for the function to process it
        "node_info": {
            "tools_to_use": [{
                "tool_params": {
                    "items": [
                        {
                            "field_name": "start_value",  # Contains 'start'
                            "field_value": "1",
                            "data_type": "number"
                        },
                        {
                            "field_name": "end_value",  # Contains 'end'
                            "field_value": "5",
                            "data_type": "number"
                        },
                        {
                            "field_name": "step_increment",  # Contains 'step'
                            "field_value": "2",
                            "data_type": "number"
                        }
                    ]
                }
            }]
        },
        "loop_config": {}
    }
    
    print("\nTesting with proper field name patterns...")
    print("Before mapping:")
    print(f"  tool_params: {transition2['node_info']['tools_to_use'][0]['tool_params']}")
    
    _update_loop_config_from_tool_params(transition2, None)
    
    print("After mapping:")
    print(f"  loop_config: {transition2['loop_config']}")
    
    expected_structure = {
        "iteration_source": {
            "number_range": {
                "start": 1,
                "end": 5,
                "step": 2
            }
        }
    }
    
    if transition2["loop_config"] == expected_structure:
        print("\n✅ SUCCESS: Number range mapping worked!")
        return True
    else:
        print("\n❌ FAILURE: Number range mapping failed")
        print(f"   Expected: {expected_structure}")
        print(f"   Got: {transition2['loop_config']}")
        return False

if __name__ == "__main__":
    print("Testing intelligent loop parameter mapping...")
    print("="*50)
    
    success1 = test_intelligent_mapping()
    success2 = test_number_range_mapping()
    
    print("\n" + "="*50)
    if success1 and success2:
        print("🎉 ALL TESTS PASSED!")
        print("The intelligent mapping approach works correctly.")
        print("Users can now use any field names in their payload.")
    else:
        print("❌ Some tests failed. Check the implementation.")
